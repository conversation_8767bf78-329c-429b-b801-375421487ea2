#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
PaddleOCR功能测试脚本
"""

import os
import sys
import numpy as np
from PIL import Image, ImageDraw, ImageFont

def test_paddleocr_installation():
    """测试PaddleOCR安装"""
    print("🔤 PaddleOCR安装测试")
    print("=" * 40)
    
    try:
        from paddleocr import PaddleOCR
        print("✅ PaddleOCR 导入成功")
        
        # 初始化OCR
        print("🔧 正在初始化PaddleOCR...")
        ocr = PaddleOCR(use_textline_orientation=True, lang='ch', use_gpu=False)
        print("✅ PaddleOCR 初始化成功")
        
        return ocr
        
    except ImportError:
        print("❌ PaddleOCR 未安装")
        print("请运行: python install_paddleocr.py")
        return None
    except Exception as e:
        print(f"❌ PaddleOCR 初始化失败: {e}")
        return None

def create_test_image():
    """创建包含中文的测试图像"""
    print("\n🖼️ 创建测试图像...")
    
    # 创建白色背景图像
    width, height = 400, 300
    image = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(image)
    
    # 尝试使用中文字体
    try:
        # Windows
        font_path = "C:/Windows/Fonts/simhei.ttf"
        if not os.path.exists(font_path):
            # macOS
            font_path = "/System/Library/Fonts/PingFang.ttc"
            if not os.path.exists(font_path):
                # Linux
                font_path = "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"
        
        if os.path.exists(font_path):
            font_large = ImageFont.truetype(font_path, 32)
            font_medium = ImageFont.truetype(font_path, 24)
        else:
            font_large = ImageFont.load_default()
            font_medium = ImageFont.load_default()
    except:
        font_large = ImageFont.load_default()
        font_medium = ImageFont.load_default()
    
    # 绘制测试文字
    texts = [
        ("普陀山", 50, 50, font_large),
        ("观音菩萨", 50, 100, font_medium),
        ("早安", 250, 50, font_large),
        ("心存善念", 250, 100, font_medium),
        ("佛佑众生", 50, 150, font_medium),
        ("南无阿弥陀佛", 50, 200, font_medium)
    ]
    
    for text, x, y, font in texts:
        draw.text((x, y), text, fill='black', font=font)
    
    # 保存测试图像
    test_image_path = "paddleocr_test_image.png"
    image.save(test_image_path)
    print(f"✅ 测试图像已保存: {test_image_path}")
    
    return image, test_image_path

def test_paddleocr_recognition(ocr, image):
    """测试PaddleOCR识别"""
    print("\n🔍 测试PaddleOCR识别...")
    
    try:
        # 转换为numpy数组
        img_array = np.array(image)
        
        # 进行OCR识别
        result = ocr.ocr(img_array, cls=True)
        
        if result and result[0]:
            print(f"✅ PaddleOCR识别成功! 识别到 {len(result[0])} 个文字区域:")
            
            for i, line in enumerate(result[0], 1):
                bbox_points = line[0]  # 四个角点坐标
                text_info = line[1]    # (文字, 置信度)
                
                text = text_info[0]
                confidence = text_info[1]
                
                # 计算边界框
                x_coords = [point[0] for point in bbox_points]
                y_coords = [point[1] for point in bbox_points]
                x = int(min(x_coords))
                y = int(min(y_coords))
                w = int(max(x_coords) - min(x_coords))
                h = int(max(y_coords) - min(y_coords))
                
                print(f"  {i}. '{text}' (置信度: {confidence:.3f}, 位置: {x},{y},{w},{h})")
            
            return True
        else:
            print("❌ PaddleOCR 未识别到文字")
            return False
            
    except Exception as e:
        print(f"❌ PaddleOCR 识别失败: {e}")
        return False

def test_psd_processing():
    """测试PSD文件处理"""
    print("\n📄 测试PSD文件处理...")
    
    psd_file = "未标题-1.psd"
    if not os.path.exists(psd_file):
        print(f"⚠️ 未找到PSD文件: {psd_file}")
        print("请确保PSD文件在当前目录中")
        return False
    
    # 运行PaddleOCR版本的处理脚本
    print(f"🔧 正在使用PaddleOCR处理: {psd_file}")
    
    import subprocess
    result = subprocess.run([
        sys.executable, "123_paddle.py", psd_file, "output_paddle"
    ], capture_output=True, text=True, encoding='utf-8', errors='ignore')
    
    if result.returncode == 0:
        print("✅ PSD处理成功!")
        print("\n处理输出:")
        print(result.stdout)
        
        # 检查生成的INI文件
        ini_file = "output_paddle/未标题-1/未标题-1.ini"
        if os.path.exists(ini_file):
            with open(ini_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            paddle_ocr_count = content.count('source=PaddleOCR识别')
            print(f"\n📊 PaddleOCR识别统计: {paddle_ocr_count} 个文字区域")
            
            if paddle_ocr_count > 0:
                print("✅ PaddleOCR成功识别到文字!")
                
                # 显示识别到的文字
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if 'source=PaddleOCR识别' in line:
                        # 查找对应的content行
                        for j in range(max(0, i-10), min(len(lines), i+1)):
                            if lines[j].startswith('content = '):
                                text = lines[j].replace('content = ', '')
                                print(f"  - 识别文字: '{text}'")
                                break
            else:
                print("⚠️ PaddleOCR未识别到文字")
        
        return True
    else:
        print("❌ PSD处理失败!")
        print("错误输出:")
        print(result.stderr)
        return False

def compare_with_tesseract():
    """与Tesseract结果对比"""
    print("\n📊 与Tesseract结果对比...")
    
    paddle_ini = "output_paddle/未标题-1/未标题-1.ini"
    tesseract_ini = "output/未标题-1/未标题-1.ini"
    
    if os.path.exists(paddle_ini) and os.path.exists(tesseract_ini):
        # 读取PaddleOCR结果
        with open(paddle_ini, 'r', encoding='utf-8') as f:
            paddle_content = f.read()
        paddle_count = paddle_content.count('source=PaddleOCR识别')
        
        # 读取Tesseract结果
        with open(tesseract_ini, 'r', encoding='utf-8') as f:
            tesseract_content = f.read()
        tesseract_count = tesseract_content.count('source=OCR识别')
        
        print(f"PaddleOCR识别数量: {paddle_count}")
        print(f"Tesseract识别数量: {tesseract_count}")
        
        if paddle_count > 0 and tesseract_count > paddle_count:
            print("✅ PaddleOCR过滤了更多无效识别，质量更高!")
        elif paddle_count > tesseract_count:
            print("✅ PaddleOCR识别到了更多有效文字!")
        elif paddle_count == tesseract_count:
            print("ℹ️ 两种方法识别数量相同")
        else:
            print("⚠️ PaddleOCR识别数量较少，可能需要调整参数")
    else:
        print("无法比较：缺少对比文件")

def main():
    """主测试函数"""
    print("🚀 PaddleOCR功能完整测试")
    print("=" * 50)
    
    # 1. 测试安装
    ocr = test_paddleocr_installation()
    if not ocr:
        print("\n❌ PaddleOCR未正确安装，请先运行安装脚本")
        return False
    
    # 2. 测试基本识别
    test_image, test_image_path = create_test_image()
    recognition_success = test_paddleocr_recognition(ocr, test_image)
    
    # 3. 测试PSD处理
    psd_success = test_psd_processing()
    
    # 4. 与Tesseract对比
    compare_with_tesseract()
    
    # 清理测试文件
    try:
        os.remove(test_image_path)
        print(f"\n🗑️ 已清理测试文件: {test_image_path}")
    except:
        pass
    
    # 总结
    print(f"\n{'='*50}")
    print("📋 测试总结:")
    print(f"  - PaddleOCR安装: {'✅ 成功' if ocr else '❌ 失败'}")
    print(f"  - 基本识别测试: {'✅ 成功' if recognition_success else '❌ 失败'}")
    print(f"  - PSD文件处理: {'✅ 成功' if psd_success else '❌ 失败'}")
    
    if ocr and recognition_success and psd_success:
        print("\n🎉 PaddleOCR功能完全正常!")
        print("现在可以使用高质量的中文OCR识别功能了!")
    else:
        print("\n⚠️ 部分功能存在问题，请检查安装或配置")
    
    return ocr and recognition_success and psd_success

if __name__ == "__main__":
    try:
        success = main()
        
        print(f"\n{'='*50}")
        if success:
            print("🚀 现在可以使用PaddleOCR版本的工具:")
            print("   python psd_gui_tool.py  # GUI版本（自动使用PaddleOCR）")
            print("   python 123_paddle.py <psd文件> <输出目录>  # 命令行版本")
        else:
            print("🔧 请解决上述问题后重新测试")
        
        input("\n按回车键退出...")
        
    except KeyboardInterrupt:
        print("\n\n❌ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        input("\n按回车键退出...")
