from psd_tools import PSDImage
from paddleocr import PaddleOCR
from PIL import Image
import os

# 初始化 OCR
#ocr = PaddleOCR(use_angle_cls=True, lang='ch')  # 中文识别

ocr = PaddleOCR(use_textline_orientation=True, lang='ch')


# 打开 PSD 文件
psd = PSDImage.open('example.psd')

# 创建输出文件夹
os.makedirs('output_layers', exist_ok=True)

# 遍历图层
for i, layer in enumerate(psd.descendants()):
    if not layer.is_visible():
        continue  # 跳过不可见图层

    # 只处理有像素内容的图层（不是文字图层）
    try:
        image = layer.composite()  # 获取图层渲染后的图像（PIL Image）
        if image is None:
            continue

        layer_name = layer.name or f'layer_{i}'
        img_path = f'output_layers/{layer_name}.png'
        image.save(img_path)

        # OCR 识别
        result = ocr.ocr(img_path, cls=True, textline_orientation=True)
        print(f"\n图层 {layer_name} 中识别结果：")
        for line in result:
            for box in line:
                text = box[1][0]
                score = box[1][1]
                print(f"  - {text} (score: {score:.2f})")

    except Exception as e:
        print(f"跳过图层 {layer.name}，原因：{e}")
