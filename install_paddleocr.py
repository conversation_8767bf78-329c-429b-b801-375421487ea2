#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
PaddleOCR安装脚本
安装PaddleOCR及其依赖
"""

import subprocess
import sys
import os

def run_command(command):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, encoding='utf-8')
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def install_package(package_name, description=""):
    """安装Python包"""
    print(f"\n📦 正在安装 {package_name}...")
    if description:
        print(f"   {description}")
    
    success, stdout, stderr = run_command(f"{sys.executable} -m pip install {package_name}")
    
    if success:
        print(f"✅ {package_name} 安装成功!")
        return True
    else:
        print(f"❌ {package_name} 安装失败:")
        print(f"   错误信息: {stderr}")
        return False

def check_package(package_name):
    """检查包是否已安装"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False

def main():
    print("🔤 PaddleOCR安装脚本")
    print("=" * 50)
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"🐍 Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 7):
        print("❌ 错误: 需要Python 3.7或更高版本")
        return False
    
    # 升级pip
    print("\n🔧 升级pip...")
    run_command(f"{sys.executable} -m pip install --upgrade pip")
    
    # 安装依赖包
    packages = [
        ("paddlepaddle==2.5.1", "PaddlePaddle - 深度学习框架"),
        ("paddleocr==3.0.0", "PaddleOCR - 高精度OCR工具"),
        ("Pillow==10.1.0", "PIL - Python图像库"),
        ("opencv-python==********", "OpenCV - 图像处理库"),
        ("numpy==1.26.2", "NumPy - 数值计算库")
    ]
    
    print("\n📚 安装PaddleOCR及依赖...")
    
    failed_packages = []
    for package, desc in packages:
        package_name = package.split('==')[0]
        if not check_package(package_name.replace('-', '_')):
            if not install_package(package, desc):
                failed_packages.append(package)
        else:
            print(f"✅ {package_name} 已安装")
    
    # 测试PaddleOCR
    print("\n🧪 测试PaddleOCR...")
    
    try:
        from paddleocr import PaddleOCR
        print("✅ PaddleOCR 导入成功")
        
        print("   正在初始化OCR引擎...")
        # 使用新的参数名称
        ocr = PaddleOCR(use_textline_orientation=True, lang='ch', use_gpu=False)
        print("✅ PaddleOCR 初始化成功!")
        
        # 创建测试图像
        from PIL import Image, ImageDraw, ImageFont
        import numpy as np
        
        # 创建简单测试图像
        img = Image.new('RGB', (200, 100), 'white')
        draw = ImageDraw.Draw(img)
        
        try:
            # 尝试使用系统字体
            font = ImageFont.truetype("C:/Windows/Fonts/simhei.ttf", 24)
        except:
            font = ImageFont.load_default()
        
        draw.text((20, 30), "测试文字", fill='black', font=font)
        
        # 转换为numpy数组
        img_array = np.array(img)
        
        # 进行OCR识别
        result = ocr.ocr(img_array, cls=True)
        
        if result and result[0]:
            print("✅ PaddleOCR 测试成功!")
            for line in result[0]:
                text = line[1][0]
                confidence = line[1][1]
                print(f"   识别结果: '{text}' (置信度: {confidence:.2f})")
        else:
            print("⚠️ PaddleOCR 测试未识别到文字（正常，测试图像简单）")
        
    except Exception as e:
        print(f"❌ PaddleOCR 测试失败: {e}")
        failed_packages.append("paddleocr")
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 安装总结:")
    
    if not failed_packages:
        print("✅ 所有包安装成功!")
        print("🎉 PaddleOCR已就绪，识别效果将大幅提升!")
        print("\n🔤 PaddleOCR特点:")
        print("   - 中文识别准确率更高")
        print("   - 支持倾斜文字识别")
        print("   - 无需额外语言包配置")
        print("   - 基于深度学习，效果优秀")
    else:
        print(f"❌ 以下包安装失败: {', '.join(failed_packages)}")
        print("   请检查网络连接或手动安装")
    
    print("\n🚀 现在可以运行PSD处理工具:")
    print("   python psd_gui_tool.py  # GUI版本")
    print("   python 123_paddle.py <psd文件> <输出目录>  # 命令行版本")
    
    return len(failed_packages) == 0

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎉 安装完成! PaddleOCR功能已启用")
        else:
            print("\n⚠️ 安装完成，但部分功能可能不可用")
        
        input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n❌ 安装被用户中断")
    except Exception as e:
        print(f"\n❌ 安装过程中出现错误: {e}")
        input("\n按回车键退出...")
