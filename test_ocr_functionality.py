#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
OCR功能测试脚本
测试OCR文字识别功能是否正常工作
"""

import os
import sys
import numpy as np
from PIL import Image, ImageDraw, ImageFont

# 检查OCR库
try:
    import easyocr
    EASYOCR_AVAILABLE = True
except ImportError:
    EASYOCR_AVAILABLE = False

try:
    import pytesseract
    TESSERACT_AVAILABLE = True
except ImportError:
    TESSERACT_AVAILABLE = False

def create_test_image():
    """创建测试图像"""
    # 创建白色背景图像
    width, height = 400, 200
    image = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(image)
    
    # 尝试使用系统字体
    try:
        # Windows
        font_path = "C:/Windows/Fonts/simhei.ttf"
        if not os.path.exists(font_path):
            # macOS
            font_path = "/System/Library/Fonts/PingFang.ttc"
            if not os.path.exists(font_path):
                # Linux
                font_path = "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"
        
        if os.path.exists(font_path):
            font = ImageFont.truetype(font_path, 24)
        else:
            font = ImageFont.load_default()
    except:
        font = ImageFont.load_default()
    
    # 绘制测试文字
    texts = [
        ("早安", 50, 30),
        ("心存善念", 50, 70),
        ("佛佑众生", 50, 110),
        ("Hello World", 250, 30),
        ("OCR Test", 250, 70),
        ("123456", 250, 110)
    ]
    
    for text, x, y in texts:
        draw.text((x, y), text, fill='black', font=font)
    
    return image

def test_easyocr(image):
    """测试EasyOCR"""
    if not EASYOCR_AVAILABLE:
        return False, "EasyOCR未安装"
    
    try:
        print("🔍 测试EasyOCR...")
        reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)
        
        # 转换图像格式
        img_array = np.array(image)
        
        # 进行OCR识别
        results = reader.readtext(img_array)
        
        print(f"✅ EasyOCR识别结果 ({len(results)}个):")
        for i, (bbox, text, confidence) in enumerate(results, 1):
            print(f"  {i}. '{text}' (置信度: {confidence:.2f})")
        
        return True, f"成功识别{len(results)}个文字区域"
        
    except Exception as e:
        return False, f"EasyOCR测试失败: {str(e)}"

def test_tesseract(image):
    """测试Tesseract"""
    if not TESSERACT_AVAILABLE:
        return False, "Tesseract未安装"
    
    try:
        print("🔍 测试Tesseract...")
        
        # 配置参数
        config = '--oem 3 --psm 6'
        
        # 获取文本
        text = pytesseract.image_to_string(image, config=config, lang='chi_sim+eng')
        
        # 获取详细信息
        data = pytesseract.image_to_data(image, config=config, output_type=pytesseract.Output.DICT, lang='chi_sim+eng')
        
        # 过滤有效文本
        valid_texts = []
        for i in range(len(data['text'])):
            text_item = data['text'][i].strip()
            conf = int(data['conf'][i])
            if conf > 30 and len(text_item) > 0:
                valid_texts.append((text_item, conf))
        
        print(f"✅ Tesseract识别结果 ({len(valid_texts)}个):")
        for i, (text_item, conf) in enumerate(valid_texts, 1):
            print(f"  {i}. '{text_item}' (置信度: {conf})")
        
        return True, f"成功识别{len(valid_texts)}个文字区域"
        
    except Exception as e:
        return False, f"Tesseract测试失败: {str(e)}"

def test_ocr_processor():
    """测试OCR处理器类"""
    try:
        # 导入OCR处理器
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from 123 import OCRProcessor
        
        print("🔍 测试OCRProcessor类...")
        
        # 创建处理器实例
        processor = OCRProcessor()
        
        if processor.ocr_reader is None:
            return False, "OCR处理器初始化失败"
        
        # 创建测试图像
        test_image = create_test_image()
        
        # 进行OCR识别
        results = processor.extract_text_from_image(test_image, "测试图层")
        
        print(f"✅ OCRProcessor识别结果 ({len(results)}个):")
        for i, result in enumerate(results, 1):
            text = result['text']
            confidence = result['confidence']
            bbox = result['bbox']
            print(f"  {i}. '{text}' (置信度: {confidence:.2f}, 位置: {bbox})")
        
        return True, f"OCRProcessor成功识别{len(results)}个文字区域"
        
    except Exception as e:
        return False, f"OCRProcessor测试失败: {str(e)}"

def main():
    """主测试函数"""
    print("🔤 OCR功能测试")
    print("=" * 50)
    
    # 检查依赖
    print("📋 检查OCR库安装状态:")
    print(f"  EasyOCR: {'✅ 已安装' if EASYOCR_AVAILABLE else '❌ 未安装'}")
    print(f"  Tesseract: {'✅ 已安装' if TESSERACT_AVAILABLE else '❌ 未安装'}")
    
    if not EASYOCR_AVAILABLE and not TESSERACT_AVAILABLE:
        print("\n❌ 错误: 未安装任何OCR库")
        print("请运行: python install_ocr_dependencies.py")
        return False
    
    # 创建测试图像
    print("\n🖼️ 创建测试图像...")
    test_image = create_test_image()
    
    # 保存测试图像
    test_image_path = "ocr_test_image.png"
    test_image.save(test_image_path)
    print(f"✅ 测试图像已保存: {test_image_path}")
    
    # 测试结果
    test_results = []
    
    # 测试EasyOCR
    if EASYOCR_AVAILABLE:
        print("\n" + "-" * 30)
        success, message = test_easyocr(test_image)
        test_results.append(("EasyOCR", success, message))
    
    # 测试Tesseract
    if TESSERACT_AVAILABLE:
        print("\n" + "-" * 30)
        success, message = test_tesseract(test_image)
        test_results.append(("Tesseract", success, message))
    
    # 测试OCR处理器
    print("\n" + "-" * 30)
    success, message = test_ocr_processor()
    test_results.append(("OCRProcessor", success, message))
    
    # 显示测试总结
    print("\n" + "=" * 50)
    print("📊 测试总结:")
    
    all_passed = True
    for name, success, message in test_results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {name}: {status} - {message}")
        if not success:
            all_passed = False
    
    print(f"\n🎯 总体结果: {'✅ 所有测试通过' if all_passed else '❌ 部分测试失败'}")
    
    if all_passed:
        print("\n🎉 OCR功能正常，可以在PSD处理工具中使用!")
    else:
        print("\n⚠️ 请检查OCR库安装或配置")
    
    # 清理测试文件
    try:
        os.remove(test_image_path)
        print(f"\n🗑️ 已清理测试文件: {test_image_path}")
    except:
        pass
    
    return all_passed

if __name__ == "__main__":
    try:
        success = main()
        print(f"\n{'='*50}")
        if success:
            print("🚀 现在可以使用增强的PSD处理工具:")
            print("   python psd_gui_tool.py")
            print("   python 123.py <psd文件> <输出目录>")
        else:
            print("🔧 请先解决OCR配置问题")
        
        input("\n按回车键退出...")
        
    except KeyboardInterrupt:
        print("\n\n❌ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        input("\n按回车键退出...")
