#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速OCR测试脚本
用于快速测试OCR功能是否能识别图片中的文字
"""

import os
import sys
from PIL import Image
import numpy as np

# 检查OCR库
try:
    import pytesseract
    TESSERACT_AVAILABLE = True
    print("✅ Tesseract 可用")
except ImportError:
    TESSERACT_AVAILABLE = False
    print("❌ Tesseract 不可用")

try:
    import easyocr
    EASYOCR_AVAILABLE = True
    print("✅ EasyOCR 可用")
except ImportError:
    EASYOCR_AVAILABLE = False
    print("❌ EasyOCR 不可用")

def test_tesseract_on_image(image_path):
    """使用Tesseract测试图像"""
    if not TESSERACT_AVAILABLE:
        print("❌ Tesseract 不可用")
        return
    
    try:
        print(f"\n🔍 使用Tesseract测试: {os.path.basename(image_path)}")
        
        # 打开图像
        image = Image.open(image_path)
        print(f"📊 图像信息: {image.size}, 模式: {image.mode}")
        
        # 尝试多种配置
        configs = [
            '--oem 3 --psm 6',  # 统一文本块
            '--oem 3 --psm 8',  # 单词识别
            '--oem 3 --psm 7',  # 单行文本
            '--oem 3 --psm 13'  # 原始行
        ]
        
        all_results = []
        
        for i, config in enumerate(configs, 1):
            try:
                print(f"\n  配置 {i}: {config}")
                
                # 获取详细信息
                data = pytesseract.image_to_data(image, config=config, output_type=pytesseract.Output.DICT, lang='chi_sim+eng')
                
                config_results = []
                for j in range(len(data['text'])):
                    text = data['text'][j].strip()
                    conf = int(data['conf'][j]) if data['conf'][j] != -1 else 0
                    
                    if conf > 20 and len(text) > 0:
                        x, y, w, h = data['left'][j], data['top'][j], data['width'][j], data['height'][j]
                        config_results.append({
                            'text': text,
                            'confidence': conf,
                            'bbox': (x, y, w, h)
                        })
                
                if config_results:
                    print(f"    ✅ 识别到 {len(config_results)} 个文字:")
                    for result in config_results:
                        print(f"      - '{result['text']}' (置信度: {result['confidence']})")
                    all_results.extend(config_results)
                else:
                    print(f"    ❌ 未识别到文字")
                    
            except Exception as e:
                print(f"    ❌ 配置失败: {str(e)}")
        
        # 去重
        unique_texts = set()
        final_results = []
        for result in sorted(all_results, key=lambda x: x['confidence'], reverse=True):
            if result['text'] not in unique_texts:
                unique_texts.add(result['text'])
                final_results.append(result)
        
        print(f"\n🎯 Tesseract 最终结果 ({len(final_results)} 个):")
        for result in final_results:
            print(f"  - '{result['text']}' (置信度: {result['confidence']})")
            
    except Exception as e:
        print(f"❌ Tesseract 测试失败: {str(e)}")

def test_easyocr_on_image(image_path):
    """使用EasyOCR测试图像"""
    if not EASYOCR_AVAILABLE:
        print("❌ EasyOCR 不可用")
        return
    
    try:
        print(f"\n🔍 使用EasyOCR测试: {os.path.basename(image_path)}")
        
        # 初始化EasyOCR
        reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)
        
        # 读取图像
        image = Image.open(image_path)
        img_array = np.array(image)
        
        print(f"📊 图像信息: {image.size}, 模式: {image.mode}")
        
        # 进行OCR识别
        results = reader.readtext(img_array)
        
        if results:
            print(f"🎯 EasyOCR 识别结果 ({len(results)} 个):")
            for i, (bbox, text, confidence) in enumerate(results, 1):
                print(f"  {i}. '{text}' (置信度: {confidence:.2f})")
        else:
            print("❌ EasyOCR 未识别到文字")
            
    except Exception as e:
        print(f"❌ EasyOCR 测试失败: {str(e)}")

def main():
    """主函数"""
    print("🔤 快速OCR测试工具")
    print("=" * 40)
    
    if not TESSERACT_AVAILABLE and not EASYOCR_AVAILABLE:
        print("❌ 错误: 未安装任何OCR库")
        print("请运行: python install_ocr_dependencies.py")
        return
    
    # 查找调试图像
    debug_images = []
    
    # 在output目录中查找调试图像
    if os.path.exists("output"):
        for root, dirs, files in os.walk("output"):
            for file in files:
                if file.startswith("debug_ocr_") and file.endswith(".png"):
                    debug_images.append(os.path.join(root, file))
    
    # 查找其他图像文件
    for ext in ['.png', '.jpg', '.jpeg']:
        for file in os.listdir('.'):
            if file.lower().endswith(ext):
                debug_images.append(file)
    
    if not debug_images:
        print("❌ 未找到图像文件进行测试")
        print("请先运行PSD处理工具生成调试图像")
        return
    
    print(f"📁 找到 {len(debug_images)} 个图像文件:")
    for i, img in enumerate(debug_images, 1):
        print(f"  {i}. {img}")
    
    # 测试每个图像
    for image_path in debug_images:
        print(f"\n{'='*60}")
        print(f"🖼️ 测试图像: {image_path}")
        
        if not os.path.exists(image_path):
            print(f"❌ 文件不存在: {image_path}")
            continue
        
        # 测试Tesseract
        if TESSERACT_AVAILABLE:
            test_tesseract_on_image(image_path)
        
        # 测试EasyOCR
        if EASYOCR_AVAILABLE:
            test_easyocr_on_image(image_path)
    
    print(f"\n{'='*60}")
    print("🎯 测试完成!")
    print("\n💡 提示:")
    print("1. 如果识别效果不好，可能是图像质量问题")
    print("2. 尝试提高图像对比度或分辨率")
    print("3. 确保文字清晰可见")

if __name__ == "__main__":
    try:
        main()
        input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n❌ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        input("\n按回车键退出...")
