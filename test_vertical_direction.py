#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试竖排方向判断功能（rtl vs ltr）
"""

def test_vertical_direction_logic():
    """测试竖排方向判断逻辑"""
    print("🧪 测试竖排方向判断逻辑")
    print("=" * 60)
    
    # 模拟detect_vertical_direction函数
    def detect_vertical_direction(text_content, position_x, psd_width):
        """检测竖排方向：rtl(右到左) 或 ltr(左到右)"""
        
        # 1. 基于文本内容的判断
        traditional_keywords = ['佛', '禅', '道', '古', '典', '雅', '墨', '书', '法', '诗', '词', '赋', '经', '文']
        modern_keywords = ['设计', '创意', '时尚', '科技', '数字', '网络', '现代', '潮流', '品牌']
        
        traditional_score = sum(1 for keyword in traditional_keywords if keyword in text_content)
        modern_score = sum(1 for keyword in modern_keywords if keyword in text_content)
        
        # 2. 基于位置的判断
        position_ratio = position_x / psd_width if psd_width > 0 else 0.5
        
        # 判断逻辑
        if traditional_score > modern_score:
            direction = 'rtl'
            reason = f"包含传统词汇({traditional_score}个)，判断为传统竖排(右到左)"
        elif modern_score > traditional_score:
            direction = 'ltr'
            reason = f"包含现代词汇({modern_score}个)，判断为现代竖排(左到右)"
        elif position_ratio > 0.6:
            # 文字在右侧，可能是传统竖排
            direction = 'rtl'
            reason = f"文字位置偏右({position_ratio:.2f})，倾向于传统竖排(右到左)"
        elif position_ratio < 0.4:
            # 文字在左侧，可能是现代竖排
            direction = 'ltr'
            reason = f"文字位置偏左({position_ratio:.2f})，倾向于现代竖排(左到右)"
        else:
            # 默认使用传统竖排（更常见于中文设计）
            direction = 'rtl'
            reason = f"无明确特征，默认使用传统竖排(右到左)"
        
        return direction, reason
    
    # 测试用例
    test_cases = [
        # (文本内容, X位置, PSD宽度, 预期方向, 描述)
        
        # 基于文本内容的判断
        ("世界和阳光都刚刚醒来，温柔地包裹着你。", 500, 1000, "rtl", "普通文本，位置居中"),
        ("佛法无边，普度众生", 500, 1000, "rtl", "包含传统词汇'佛'"),
        ("古典诗词赋", 500, 1000, "rtl", "包含多个传统词汇"),
        ("现代设计理念", 500, 1000, "ltr", "包含现代词汇"),
        ("科技创新品牌", 500, 1000, "ltr", "包含多个现代词汇"),
        
        # 基于位置的判断
        ("早安", 100, 1000, "ltr", "文字在左侧(10%)，现代竖排"),
        ("早安", 300, 1000, "ltr", "文字在左侧(30%)，现代竖排"),
        ("早安", 700, 1000, "rtl", "文字在右侧(70%)，传统竖排"),
        ("早安", 900, 1000, "rtl", "文字在右侧(90%)，传统竖排"),
        
        # 边界情况
        ("早安", 500, 1000, "rtl", "文字居中，默认传统竖排"),
        ("", 100, 1000, "ltr", "无文本内容，位置偏左"),
        ("", 800, 1000, "rtl", "无文本内容，位置偏右"),
        ("", 500, 1000, "rtl", "无文本内容，位置居中，默认传统"),
        
        # 实际场景
        ("世界和阳光都刚刚醒来，温柔地包裹着你。", 800, 1104, "rtl", "您图片中的红框文字（右侧）"),
        ("早安", 352, 1104, "ltr", "您图片中的早安文字（偏左）"),
        ("心存善念 佛佑众生", 315, 1104, "rtl", "包含'佛'字的传统文本"),
    ]
    
    print("📋 测试用例:")
    print("-" * 60)
    
    success_count = 0
    
    for i, (text, pos_x, psd_w, expected, description) in enumerate(test_cases):
        direction, reason = detect_vertical_direction(text, pos_x, psd_w)
        
        result_icon = "✅" if direction == expected else "❌"
        position_ratio = pos_x / psd_w if psd_w > 0 else 0.5
        
        print(f"{i+1:2d}. {result_icon} {description}")
        print(f"    文本: '{text}'")
        print(f"    位置: {pos_x}/{psd_w} ({position_ratio:.1%})")
        print(f"    判断: {direction} ({reason})")
        print(f"    预期: {expected}")
        
        if direction == expected:
            success_count += 1
        
        print()
    
    print(f"📊 测试结果: {success_count}/{len(test_cases)} 通过 ({success_count/len(test_cases)*100:.1f}%)")
    
    return success_count == len(test_cases)

def test_full_orientation_logic():
    """测试完整的排版方向判断逻辑"""
    print("\n🔄 测试完整的排版方向判断逻辑")
    print("=" * 60)
    
    # 模拟完整的detect_text_orientation函数
    def detect_text_orientation(width, height, text_content="", position_x=0, psd_width=0):
        """完整的排版方向判断"""
        aspect_ratio = width / height if height > 0 else 1
        has_chinese = any('\u4e00' <= c <= '\u9fff' for c in text_content) if text_content else False
        text_length = len(text_content.strip()) if text_content else 0
        
        def detect_vertical_direction(text_content, position_x, psd_width):
            traditional_keywords = ['佛', '禅', '道', '古', '典', '雅', '墨', '书', '法', '诗', '词', '赋', '经', '文']
            modern_keywords = ['设计', '创意', '时尚', '科技', '数字', '网络', '现代', '潮流', '品牌']
            
            traditional_score = sum(1 for keyword in traditional_keywords if keyword in text_content)
            modern_score = sum(1 for keyword in modern_keywords if keyword in text_content)
            position_ratio = position_x / psd_width if psd_width > 0 else 0.5
            
            if traditional_score > modern_score:
                return 'rtl'
            elif modern_score > traditional_score:
                return 'ltr'
            elif position_ratio > 0.6:
                return 'rtl'
            elif position_ratio < 0.4:
                return 'ltr'
            else:
                return 'rtl'
        
        # 判断逻辑
        if text_content and has_chinese:
            if aspect_ratio > 3.0:
                return 'horizontal'
            elif aspect_ratio < 0.4:
                vertical_direction = detect_vertical_direction(text_content, position_x, psd_width)
                return f'vertical-{vertical_direction}'
            elif text_length <= 3 and aspect_ratio < 2.5:
                vertical_direction = detect_vertical_direction(text_content, position_x, psd_width)
                return f'vertical-{vertical_direction}'
            elif aspect_ratio > 2.0:
                return 'horizontal'
            else:
                vertical_direction = detect_vertical_direction(text_content, position_x, psd_width)
                return f'vertical-{vertical_direction}'
        else:
            if aspect_ratio > 2.0:
                return 'horizontal'
            elif height > width:
                return 'vertical-ltr'
            else:
                return 'horizontal'
    
    # 测试用例
    test_cases = [
        # (宽, 高, 文本, X位置, PSD宽度, 预期结果, 描述)
        (150, 200, "世界和阳光都刚刚醒来，温柔地包裹着你。", 800, 1104, "vertical-rtl", "红框文字：右侧竖排"),
        (100, 150, "早安", 352, 1104, "vertical-ltr", "早安文字：左侧竖排"),
        (80, 120, "心存善念 佛佑众生", 315, 1104, "vertical-rtl", "包含'佛'字：传统竖排"),
        (400, 50, "世界和阳光都刚刚醒来", 100, 1000, "horizontal", "宽度明显大于高度：横排"),
        (50, 300, "古典诗词", 800, 1000, "vertical-rtl", "高度明显大于宽度：右侧传统竖排"),
        (50, 300, "现代设计", 200, 1000, "vertical-ltr", "高度明显大于宽度：左侧现代竖排"),
        (200, 100, "Hello World", 500, 1000, "horizontal", "英文文本：横排"),
        (100, 200, "", 100, 1000, "vertical-ltr", "无内容：左侧竖排"),
        (100, 200, "", 800, 1000, "vertical-ltr", "无内容：英文默认现代竖排"),
    ]
    
    print("📋 完整测试用例:")
    print("-" * 60)
    
    success_count = 0
    
    for i, (w, h, text, pos_x, psd_w, expected, description) in enumerate(test_cases):
        result = detect_text_orientation(w, h, text, pos_x, psd_w)
        
        result_icon = "✅" if result == expected else "❌"
        aspect_ratio = w / h if h > 0 else 1
        position_ratio = pos_x / psd_w if psd_w > 0 else 0.5
        
        print(f"{i+1:2d}. {result_icon} {description}")
        print(f"    尺寸: {w}x{h} (宽高比: {aspect_ratio:.2f})")
        print(f"    文本: '{text}'")
        print(f"    位置: {pos_x}/{psd_w} ({position_ratio:.1%})")
        print(f"    判断: {result}")
        print(f"    预期: {expected}")
        
        if result == expected:
            success_count += 1
        
        print()
    
    print(f"📊 完整测试结果: {success_count}/{len(test_cases)} 通过 ({success_count/len(test_cases)*100:.1f}%)")
    
    return success_count == len(test_cases)

def main():
    """主函数"""
    print("🧪 竖排方向判断功能测试")
    print("=" * 70)
    
    # 1. 测试竖排方向判断逻辑
    direction_test_success = test_vertical_direction_logic()
    
    # 2. 测试完整的排版方向判断
    full_test_success = test_full_orientation_logic()
    
    # 3. 总结
    print("\n" + "=" * 70)
    print("📊 竖排方向判断测试结果:")
    print(f"  方向判断测试: {'✅ 通过' if direction_test_success else '❌ 失败'}")
    print(f"  完整逻辑测试: {'✅ 通过' if full_test_success else '❌ 失败'}")
    
    all_success = direction_test_success and full_test_success
    
    if all_success:
        print("\n🎉 所有测试通过！竖排方向判断功能正常工作")
        print("\n✨ 新的orientation值:")
        print("  - horizontal      → 横排")
        print("  - vertical-rtl    → 竖排(右到左) - 传统方向")
        print("  - vertical-ltr    → 竖排(左到右) - 现代方向")
        print("\n🔍 判断依据:")
        print("  1. 文本内容分析 (传统词汇 vs 现代词汇)")
        print("  2. 位置分析 (左侧 vs 右侧)")
        print("  3. 默认规则 (中文倾向传统，英文倾向现代)")
    else:
        print("\n⚠️ 部分测试失败，竖排方向判断功能需要改进")
    
    return all_success

if __name__ == "__main__":
    try:
        success = main()
        input(f"\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        input("\n按回车键退出...")
