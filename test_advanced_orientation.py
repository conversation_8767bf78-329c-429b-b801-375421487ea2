#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试基于PSD图层属性的高级文本排版方向判断功能
"""

class MockLayer:
    """模拟PSD图层对象"""
    def __init__(self, name, width, height, text, transform=None, text_data=None):
        self.name = name
        self.width = width
        self.height = height
        self.text = text
        self.transform = transform
        self.text_data = text_data

class MockTransform:
    """模拟变换矩阵"""
    def __init__(self, xx=1, xy=0, yx=0, yy=1):
        self.xx = xx
        self.xy = xy
        self.yx = yx
        self.yy = yy

class MockTextData:
    """模拟文本数据"""
    def __init__(self, orientation=None, writing_direction=None):
        self.orientation = orientation
        self.writing_direction = writing_direction

def test_advanced_orientation_logic():
    """测试高级排版方向判断逻辑"""
    print("🧪 测试基于PSD图层属性的高级排版方向判断")
    print("=" * 60)
    
    # 模拟高级检测函数
    def determine_orientation_by_rules(width, height, text_content, has_rotation, text_orientation, writing_direction):
        """根据规则判断文本方向"""
        
        # 规则1: 如果检测到明确的文本方向属性
        if text_orientation:
            if 'vertical' in str(text_orientation).lower():
                return 'vertical', f"文本方向属性: {text_orientation}"
            elif 'horizontal' in str(text_orientation).lower():
                return 'horizontal', f"文本方向属性: {text_orientation}"
        
        # 规则2: 如果检测到书写方向
        if writing_direction:
            if 'vertical' in str(writing_direction).lower():
                return 'vertical', f"书写方向: {writing_direction}"
            elif 'ltr' in str(writing_direction).lower() or 'horizontal' in str(writing_direction).lower():
                return 'horizontal', f"书写方向: {writing_direction}"
        
        # 规则3: 如果检测到旋转，可能是竖排
        if has_rotation:
            return 'vertical', "检测到图层旋转"
        
        # 规则4: 基于边界框尺寸判断（核心逻辑）
        if width > height:
            return 'horizontal', f"宽度({width}) > 高度({height})"
        elif height > width:
            return 'vertical', f"高度({height}) > 宽度({width})"
        else:
            # 宽高相等的情况，使用备用逻辑
            has_chinese = any('\u4e00' <= c <= '\u9fff' for c in text_content) if text_content else False
            text_length = len(text_content.strip()) if text_content else 0
            
            if has_chinese:
                has_punctuation = any(c in '，。、；：！？' for c in text_content)
                if text_length >= 8 and has_punctuation:
                    return 'vertical', f"中文长文本({text_length}字)且包含标点"
                elif text_length <= 3:
                    return 'vertical', f"中文短文本({text_length}字)"
                else:
                    return 'horizontal', "中文中等文本"
            else:
                return 'horizontal', "英文文本默认横排"
    
    # 测试用例
    test_cases = [
        # (图层名, 宽, 高, 文本, 旋转, 文本方向, 书写方向, 预期结果, 描述)
        
        # 基于您图片中的实际场景
        ("早安图层", 401, 191, "早安", False, None, None, "horizontal", "早安：宽度>高度"),
        ("心存善念图层", 472, 54, "心存善念 佛佑众生", False, None, None, "horizontal", "心存善念：宽度明显>高度"),
        ("红框文字", 150, 200, "世界和阳光都刚刚醒来，温柔地包裹着你。", False, None, None, "vertical", "红框：高度>宽度的长文本"),
        
        # 基于PSD属性的测试
        ("属性竖排", 100, 100, "测试", False, "vertical", None, "vertical", "文本方向属性为vertical"),
        ("属性横排", 100, 100, "测试", False, "horizontal", None, "horizontal", "文本方向属性为horizontal"),
        ("书写方向竖排", 100, 100, "测试", False, None, "vertical-rl", "vertical", "书写方向为vertical-rl"),
        ("书写方向横排", 100, 100, "测试", False, None, "LTR", "horizontal", "书写方向为LTR"),
        
        # 旋转检测
        ("旋转文字", 100, 100, "旋转测试", True, None, None, "vertical", "检测到图层旋转"),
        
        # 边界情况
        ("正方形中文长文本", 100, 100, "这是一个很长的中文文本，包含标点符号。", False, None, None, "vertical", "正方形+中文长文本+标点"),
        ("正方形中文短文本", 100, 100, "短文", False, None, None, "vertical", "正方形+中文短文本"),
        ("正方形英文", 100, 100, "English Text", False, None, None, "horizontal", "正方形+英文文本"),
        
        # 极端宽高比
        ("极宽文本", 500, 50, "很宽的文本", False, None, None, "horizontal", "极宽文本"),
        ("极高文本", 50, 500, "很高的文本", False, None, None, "vertical", "极高文本"),
    ]
    
    print("📋 测试用例:")
    print("-" * 60)
    
    success_count = 0
    
    for i, (name, width, height, text, has_rotation, text_orientation, writing_direction, expected, description) in enumerate(test_cases):
        orientation, reason = determine_orientation_by_rules(
            width, height, text, has_rotation, text_orientation, writing_direction
        )
        
        result_icon = "✅" if orientation == expected else "❌"
        aspect_ratio = width / height if height > 0 else 1
        
        print(f"{i+1:2d}. {result_icon} {description}")
        print(f"    图层: '{name}' ({width}x{height}, 宽高比: {aspect_ratio:.2f})")
        print(f"    文本: '{text}'")
        print(f"    属性: 旋转={has_rotation}, 方向={text_orientation}, 书写={writing_direction}")
        print(f"    判断: {orientation} ({reason})")
        print(f"    预期: {expected}")
        
        if orientation == expected:
            success_count += 1
        
        print()
    
    print(f"📊 测试结果: {success_count}/{len(test_cases)} 通过 ({success_count/len(test_cases)*100:.1f}%)")
    
    return success_count == len(test_cases)

def main():
    """主函数"""
    print("🧪 基于PSD图层属性的高级文本排版方向判断测试")
    print("=" * 70)
    
    # 测试高级排版方向判断
    test_success = test_advanced_orientation_logic()
    
    # 总结
    print("\n" + "=" * 70)
    print("📊 高级排版方向判断测试结果:")
    print(f"  判断逻辑测试: {'✅ 通过' if test_success else '❌ 失败'}")
    
    if test_success:
        print("\n🎉 所有测试通过！高级排版方向判断功能正常工作")
        print("\n✨ 判断优先级:")
        print("  1. 文本方向属性 (textOrientation)")
        print("  2. 书写方向属性 (writingDirection)")
        print("  3. 图层旋转检测 (transform)")
        print("  4. 边界框尺寸判断 (width vs height)")
        print("  5. 文本内容分析 (备用逻辑)")
        print("\n🔍 您的逻辑实现:")
        print("  横排: textOrientation=horizontal, writingDirection=LTR, width>height")
        print("  竖排: textOrientation=vertical, writingDirection=vertical-rl, height>width")
        print("\n📋 您的PSD文件预期结果:")
        print("  - '早安' (401x191) → horizontal")
        print("  - '心存善念 佛佑众生' (472x54) → horizontal")
        print("  - 红框长文本 (150x200) → vertical")
    else:
        print("\n⚠️ 测试失败，高级排版方向判断功能需要改进")
    
    return test_success

if __name__ == "__main__":
    try:
        success = main()
        input(f"\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        input("\n按回车键退出...")
