#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
PSD文件处理工具 - Web版本
基于Flask的Web应用，支持在线PSD文件处理
"""

from flask import Flask, render_template, request, jsonify, send_file, redirect, url_for
import os
import sys
import tempfile
import zipfile
import shutil
from werkzeug.utils import secure_filename
import threading
import time
import uuid
from datetime import datetime

# 导入核心处理模块
from psd_tools import PSDImage
import numpy as np
from PIL import Image
import cv2

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024  # 100MB max file size
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['OUTPUT_FOLDER'] = 'outputs'
app.config['SECRET_KEY'] = 'psd-processor-secret-key'

# 确保目录存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['OUTPUT_FOLDER'], exist_ok=True)

# 全局任务状态
tasks = {}

# OCR引擎检测
OCR_AVAILABLE = False
OCR_TYPE = "无"

try:
    import pytesseract
    OCR_AVAILABLE = True
    OCR_TYPE = "Tesseract"
except ImportError:
    pass

class WebOCRProcessor:
    """Web版OCR处理器"""
    
    def __init__(self):
        self.ocr_available = OCR_AVAILABLE
    
    def extract_text_from_image(self, image, layer_name=""):
        """从图像中提取文字"""
        if not self.ocr_available:
            return []
        
        try:
            # 图像预处理
            if isinstance(image, Image.Image):
                img_array = np.array(image)
            else:
                img_array = image
            
            # 转换RGBA到RGB
            if len(img_array.shape) == 3 and img_array.shape[2] == 4:
                rgb_array = np.ones((img_array.shape[0], img_array.shape[1], 3), dtype=np.uint8) * 255
                alpha = img_array[:, :, 3:4] / 255.0
                rgb_array = rgb_array * (1 - alpha) + img_array[:, :, :3] * alpha
                img_array = rgb_array.astype(np.uint8)
            
            # 转换为灰度并预处理
            if len(img_array.shape) == 3:
                gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            else:
                gray = img_array
            
            # 图像增强
            blurred = cv2.GaussianBlur(gray, (3, 3), 0)
            binary = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
            kernel = np.ones((2, 2), np.uint8)
            cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
            
            text_results = []
            
            # OCR配置
            configs = [
                '--oem 3 --psm 6',
                '--oem 3 --psm 8',
                '--oem 3 --psm 7'
            ]
            
            all_results = []
            
            for config in configs:
                try:
                    data = pytesseract.image_to_data(cleaned, config=config, 
                                                   output_type=pytesseract.Output.DICT, 
                                                   lang='chi_sim+eng')
                    
                    for i in range(len(data['text'])):
                        text = data['text'][i].strip()
                        conf = int(data['conf'][i]) if data['conf'][i] != -1 else 0
                        
                        if (conf > 70 and len(text) >= 2 and self._is_valid_text(text)):
                            x, y, w, h = data['left'][i], data['top'][i], data['width'][i], data['height'][i]
                            if w >= 10 and h >= 10:
                                all_results.append({
                                    'text': text,
                                    'confidence': conf / 100.0,
                                    'bbox': (x, y, w, h),
                                    'source': f'OCR识别-{layer_name}'
                                })
                except:
                    continue
            
            # 去重
            seen_texts = set()
            for result in sorted(all_results, key=lambda x: x['confidence'], reverse=True):
                if result['text'] not in seen_texts:
                    text_results.append(result)
                    seen_texts.add(result['text'])
            
            return text_results
            
        except Exception as e:
            return []
    
    def _is_valid_text(self, text):
        """验证文本是否有效"""
        if not text or len(text.strip()) < 2:
            return False
        
        # 过滤常见误识别
        invalid_patterns = [
            'AW', 'De', 'ee', 'wae', 'fea', 'eae', 'an', 'LY', 'ied',
            'at', 'to', 'in', 'on', 'is', 'it', 'be', 'or', 'as'
        ]
        if text in invalid_patterns:
            return False
        
        # 过滤纯符号
        meaningless_chars = set('.,;:!?@#$%^&*()[]{}|\\/<>~`"\'')
        if all(c in meaningless_chars or c.isspace() for c in text):
            return False
        
        # 检查中文或有意义英文
        has_chinese = any('\u4e00' <= c <= '\u9fff' for c in text)
        has_meaningful_english = any(c.isalpha() for c in text) and len([c for c in text if c.isalpha()]) >= 2
        
        # 优先保留的关键词
        priority_keywords = [
            '普陀山', '观音', '菩萨', '佛', '早安', '善念', '佛佑', '众生',
            '早安', '晚安', '您好', '谢谢', '平安', '健康', '快乐', '幸福'
        ]
        
        if any(keyword in text for keyword in priority_keywords):
            return True
        
        return has_chinese or has_meaningful_english

def extract_text_font_info(layer):
    """
    从PSD文本图层中提取字体信息

    Args:
        layer: PSD图层对象

    Returns:
        tuple: (font_name, font_size) - 字体名称和大小
    """
    print(f"        提取字体信息开始: '{layer.name}'")

    try:
        font_name = None  # 初始为None
        font_size = None  # 初始为None

        print(f"          初始默认值: {font_name}, {font_size}pt")

        # 方法1: 优先从engine_dict提取字体信息
        if hasattr(layer, 'engine_dict') and layer.engine_dict:
            engine_dict = layer.engine_dict
            print(f"          ✓ 找到 engine_dict")

            # 路径1：尝试 ResourceDict → FontSet (如果存在)
            font_set = engine_dict.get('ResourceDict', {}).get('FontSet', [])
            run_array = engine_dict.get('StyleRun', {}).get('RunArray', [])

            if font_set and run_array:
                print(f"          ✓ FontSet 长度: {len(font_set)}, RunArray 长度: {len(run_array)}")
                first_run = run_array[0].get('StyleSheet', {})
                font_index = first_run.get('Font')
                font_size_val = first_run.get('FontSize')

                # 提取字体名称
                if isinstance(font_index, int) and font_index < len(font_set):
                    raw_font_name = font_set[font_index].get('Name')
                    if raw_font_name:
                        font_name = raw_font_name  # 直接使用PSD原始字体名
                        print(f"          ✓ 从 FontSet 提取字体: {raw_font_name}")

                # 提取字体大小
                if isinstance(font_size_val, (int, float)) and font_size_val > 0:
                    font_size = int(round(font_size_val))
                    print(f"          ✓ 从 StyleSheet 提取字号: {font_size_val} → {font_size}pt")

                if font_name or font_size:
                    print(f"          ★ FontSet路径提取成功: {font_name}, {font_size}pt")
                    return font_name, font_size

            # 路径2：直接从 StyleRun → RunArray → StyleSheet → StyleSheetData 提取字号
            elif run_array:
                print(f"          ✓ 没有FontSet，直接从StyleSheetData提取字号")
                print(f"          ✓ RunArray 长度: {len(run_array)}")

                for i, run in enumerate(run_array):
                    style_sheet = run.get('StyleSheet', {})
                    style_sheet_data = style_sheet.get('StyleSheetData', {})

                    if style_sheet_data:
                        print(f"            检查Run {i} StyleSheetData...")
                        print(f"            StyleSheetData内容: {style_sheet_data}")

                        # 提取字体大小
                        font_size_val = style_sheet_data.get('FontSize')
                        print(f"            FontSize值: {font_size_val}, 类型: {type(font_size_val)}")
                        # 支持psd_tools的特殊数值类型
                        try:
                            font_size_float = float(font_size_val)
                            if font_size_float > 0:
                                font_size = int(round(font_size_float))
                                print(f"            ✓ 从 StyleSheetData 提取字号: {font_size_val} → {font_size}pt")

                                # 字体名称无法从StyleSheetData获取，返回None
                                print(f"            ✗ StyleSheetData中无字体名称信息")
                                print(f"          ★ 部分提取成功: 字体=None, 字号={font_size}pt")
                                return None, font_size
                            else:
                                print(f"            ✗ FontSize值无效: {font_size_float}")
                        except (ValueError, TypeError) as e:
                            print(f"            ✗ FontSize转换失败: {font_size_val} (错误: {e})")
            else:
                print(f"          ✗ 没有 RunArray")

        # 方法2: 检查图层的所有可能属性（作为备用）
        print(f"          检查图层所有属性...")

        # 打印图层的所有属性，寻找字体相关信息
        print(f"          图层所有属性:")
        for attr_name in dir(layer):
            if not attr_name.startswith('_'):
                try:
                    attr_value = getattr(layer, attr_name)
                    if any(keyword in attr_name.lower() for keyword in ['font', 'size', 'style', 'text', 'engine']):
                        print(f"            {attr_name}: {attr_value}")
                except Exception as e:
                    print(f"            {attr_name}: 无法访问 ({e})")

        # 如果没有找到字体信息，返回None让调用方处理
        print(f"          ✗ 无法从PSD中提取字体信息")
        return None, None

    except Exception as e:
        print(f"        ❌字体信息提取失败: {e}")
        import traceback
        print(f"        错误详情: {traceback.format_exc()}")
        return None, None

def extract_text_color(layer):
    """
    从PSD文本图层中提取颜色信息

    Args:
        layer: PSD图层对象

    Returns:
        str: 十六进制颜色值，如 '#FF0000'
    """
    try:
        print(f"        提取文本颜色: '{layer.name}'")

        # 方法1: 优先从engine_dict中获取颜色
        if hasattr(layer, 'engine_dict') and layer.engine_dict:
            try:
                engine_dict = layer.engine_dict
                print(f"          检查engine_dict: {type(engine_dict)}")

                # 从 StyleRun → RunArray → StyleSheet → StyleSheetData → FillColor 提取颜色
                run_array = engine_dict.get('StyleRun', {}).get('RunArray', [])
                if run_array:
                    print(f"          ✓ RunArray 长度: {len(run_array)}")

                    for i, run in enumerate(run_array):
                        style_sheet = run.get('StyleSheet', {})
                        style_sheet_data = style_sheet.get('StyleSheetData', {})

                        if style_sheet_data:
                            print(f"            检查Run {i} StyleSheetData...")

                            # 检查FillColor
                            fill_color = style_sheet_data.get('FillColor')
                            if fill_color:
                                print(f"            ✓ 发现FillColor: {fill_color}")
                                color = parse_color_value(fill_color)
                                print(f"            parse_color_value返回: {color}")
                                if color and color != "#000000":
                                    print(f"            ★ 从StyleSheetData提取颜色: {color}")
                                    return color
                                else:
                                    print(f"            ✗ 颜色解析失败或为黑色: {color}")

            except Exception as e:
                print(f"          engine_dict处理失败: {e}")

        # 方法2: 尝试从图层的engine_data中获取颜色（备用）
        if hasattr(layer, 'engine_data') and layer.engine_data:
            try:
                engine_data = layer.engine_data
                print(f"          检查engine_data: {type(engine_data)}")

                # engine_data通常是字典格式，包含文本样式信息
                if isinstance(engine_data, dict):
                    # 查找颜色相关的键
                    for key, value in engine_data.items():
                        if 'color' in str(key).lower() or 'fill' in str(key).lower():
                            print(f"          发现颜色键 {key}: {value}")
                            color = parse_color_value(value)
                            if color != "#000000":
                                return color

            except Exception as e:
                print(f"          engine_data处理失败: {e}")

        # 方法2: 尝试从text_data获取颜色
        if hasattr(layer, 'text_data') and layer.text_data:
            try:
                text_data = layer.text_data

                # 检查是否有RunArray
                if hasattr(text_data, 'RunArray') and text_data.RunArray:
                    for run in text_data.RunArray:
                        if hasattr(run, 'StyleSheetData') and run.StyleSheetData:
                            style_data = run.StyleSheetData

                            # 检查常见的颜色属性
                            for attr in ['FillColor', 'fillColor', 'color', 'Color']:
                                if hasattr(style_data, attr):
                                    color_value = getattr(style_data, attr)
                                    print(f"          发现样式颜色 {attr}: {color_value}")
                                    color = parse_color_value(color_value)
                                    if color != "#000000":
                                        return color

            except Exception as e:
                print(f"          text_data处理失败: {e}")

        # 方法3: 基于图层名称推测颜色（实用的备用方案）
        layer_name = layer.name.lower()
        color_hints = {
            '红': '#FF0000',
            '绿': '#00FF00',
            '蓝': '#0000FF',
            '白': '#FFFFFF',
            '黑': '#000000',
            '黄': '#FFFF00',
            '紫': '#800080',
            '橙': '#FFA500',
            'red': '#FF0000',
            'green': '#00FF00',
            'blue': '#0000FF',
            'white': '#FFFFFF',
            'black': '#000000',
            'yellow': '#FFFF00',
            'purple': '#800080',
            'orange': '#FFA500'
        }

        for hint, color in color_hints.items():
            if hint in layer_name:
                print(f"          根据图层名称 '{layer.name}' 推测颜色: {color}")
                return color

        # 方法4: 基于文本内容推测颜色（更智能的备用方案）
        text_content = layer.text if hasattr(layer, 'text') else ""
        if text_content:
            content_color_hints = {
                '世界和阳光': '#FFD700',  # 金色，象征阳光
                '早安': '#FF6B6B',       # 温暖的红色
                '佛': '#8B4513',         # 棕色，庄重
                '善念': '#32CD32',       # 绿色，善良
                '星期': '#4169E1',       # 蓝色
                '农历': '#8B4513',       # 棕色，传统
            }

            for hint, color in content_color_hints.items():
                if hint in text_content:
                    print(f"          根据文本内容 '{text_content}' 推测颜色: {color}")
                    return color

        print(f"          未找到颜色信息，使用默认黑色")
        return "#000000"

    except Exception as e:
        print(f"        颜色提取失败: {e}")
        return "#000000"

def parse_color_value(color_value):
    """
    解析各种格式的颜色值，包括Adobe PSD格式
    """
    try:
        if isinstance(color_value, dict):
            # Adobe PSD颜色格式: {'Type': 1, 'Values': [1.0, 0.47022, 0.57081, 0.65882]}
            if 'Type' in color_value and 'Values' in color_value:
                values = color_value['Values']
                if isinstance(values, list) and len(values) >= 3:
                    # Adobe格式通常是CMYK或RGB，Type=1通常是RGB
                    if color_value.get('Type') == 1 and len(values) >= 3:
                        # RGB格式，前三个值是RGB，第四个值可能是Alpha
                        # 需要支持psd_tools的特殊数值类型
                        try:
                            r = int(float(values[0]) * 255) if float(values[0]) <= 1 else int(float(values[0]))
                            g = int(float(values[1]) * 255) if float(values[1]) <= 1 else int(float(values[1]))
                            b = int(float(values[2]) * 255) if float(values[2]) <= 1 else int(float(values[2]))
                            result = f"#{r:02X}{g:02X}{b:02X}"
                            print(f"          Adobe颜色解析: {color_value} → {result}")
                            return result
                        except (ValueError, TypeError) as e:
                            print(f"          Adobe颜色转换失败: {e}")
                            pass

            # 标准RGB颜色字典
            elif 'r' in color_value and 'g' in color_value and 'b' in color_value:
                r = int(color_value['r'] * 255) if color_value['r'] <= 1 else int(color_value['r'])
                g = int(color_value['g'] * 255) if color_value['g'] <= 1 else int(color_value['g'])
                b = int(color_value['b'] * 255) if color_value['b'] <= 1 else int(color_value['b'])
                return f"#{r:02X}{g:02X}{b:02X}"

        elif isinstance(color_value, (list, tuple)) and len(color_value) >= 3:
            # RGB数组
            r = int(color_value[0] * 255) if color_value[0] <= 1 else int(color_value[0])
            g = int(color_value[1] * 255) if color_value[1] <= 1 else int(color_value[1])
            b = int(color_value[2] * 255) if color_value[2] <= 1 else int(color_value[2])
            return f"#{r:02X}{g:02X}{b:02X}"

        elif isinstance(color_value, str) and color_value.startswith('#'):
            # 已经是十六进制颜色
            return color_value

    except Exception as e:
        print(f"          颜色解析失败: {e}")
        pass

    return "#000000"

def map_font_name(psd_font_name):
    """
    将PSD字体名称映射到系统字体名称

    Args:
        psd_font_name: PSD中的字体名称

    Returns:
        str: 映射后的字体名称
    """
    if not psd_font_name:
        return '楷体'

    # 增强字体映射表 - 支持更多系统字体
    font_mapping = {
        # 中文字体映射
        'SimSun': '宋体',
        'SimHei': '黑体',
        'KaiTi': '楷体',
        'FangSong': '仿宋',
        'Microsoft YaHei': '微软雅黑',
        'PingFang SC': '苹方',
        'Hiragino Sans GB': '冬青黑体',

        # Source Han 系列字体（常用于设计）
        'Source Han Sans CN': 'Source Han Sans CN',
        'Source Han Sans CN Medium': 'Source Han Sans CN Medium',
        'Source Han Sans CN Bold': 'Source Han Sans CN Bold',
        'Source Han Sans CN Light': 'Source Han Sans CN Light',
        'Source Han Serif CN': 'Source Han Serif CN',
        'SourceHanSansCN-Medium': 'Source Han Sans CN Medium',
        'SourceHanSansCN-Bold': 'Source Han Sans CN Bold',
        'SourceHanSansCN-Light': 'Source Han Sans CN Light',

        # 思源字体别名
        '思源黑体': 'Source Han Sans CN',
        '思源宋体': 'Source Han Serif CN',

        # 方正字体
        'FZLanTingHei': '方正兰亭黑体',
        'FZLanTingSong': '方正兰亭宋体',
        'FZShuSong': '方正书宋',
        'FZKai': '方正楷体',

        # 华文字体
        'STSong': '华文宋体',
        'STHeiti': '华文黑体',
        'STKaiti': '华文楷体',
        'STFangsong': '华文仿宋',
        'STXihei': '华文细黑',
        'STZhongsong': '华文中宋',

        # 英文字体映射
        'Arial': 'Arial',
        'Times New Roman': 'Times New Roman',
        'Helvetica': 'Helvetica',
        'Georgia': 'Georgia',
        'Verdana': 'Verdana',
        'Calibri': 'Calibri',
        'Tahoma': 'Tahoma',
        'Trebuchet MS': 'Trebuchet MS',
        'Comic Sans MS': 'Comic Sans MS',
        'Impact': 'Impact',
        'Lucida Console': 'Lucida Console',
        'Courier New': 'Courier New',

        # 常见设计字体
        'Adobe Song Std': '宋体',
        'Adobe Heiti Std': '黑体',
        'Adobe Kaiti Std': '楷体',
        'Adobe Fangsong Std': '仿宋',
        'Adobe Garamond Pro': 'Adobe Garamond Pro',
        'Adobe Caslon Pro': 'Adobe Caslon Pro',

        # 其他常见字体
        'Noto Sans CJK SC': 'Noto Sans CJK SC',
        'Noto Serif CJK SC': 'Noto Serif CJK SC',
        'WenQuanYi Micro Hei': '文泉驿微米黑',
        'WenQuanYi Zen Hei': '文泉驿正黑',
    }

    # 直接映射
    if psd_font_name in font_mapping:
        return font_mapping[psd_font_name]

    # 模糊匹配
    psd_lower = psd_font_name.lower()
    for psd_key, mapped_font in font_mapping.items():
        if psd_key.lower() in psd_lower or psd_lower in psd_key.lower():
            return mapped_font

    # 如果没有找到映射，返回原始字体名
    return psd_font_name

def estimate_ocr_font(text):
    """
    基于OCR识别的文本内容推测合适的字体
    """
    # 检查是否包含中文
    has_chinese = any('\u4e00' <= c <= '\u9fff' for c in text) if text else False

    if has_chinese:
        # 中文文本的字体推测
        if any(keyword in text for keyword in ['佛', '禅', '道', '古', '典', '雅', '诗', '词', '赋', '经']):
            return '楷体'  # 传统文本使用楷体
        elif any(keyword in text for keyword in ['现代', '科技', '数字', '网络', '品牌']):
            return '微软雅黑'  # 现代文本使用雅黑
        elif any(keyword in text for keyword in ['标题', '题目', '主题']):
            return '黑体'  # 标题使用黑体
        else:
            return '宋体'  # 默认中文字体
    else:
        # 英文文本的字体推测
        if text.isupper():
            return 'Arial'  # 全大写使用Arial
        elif any(char.isdigit() for char in text):
            return 'Verdana'  # 包含数字使用Verdana
        else:
            return 'Times New Roman'  # 默认英文字体

def estimate_ocr_color(text, layer_name="", text_layers=None):
    """
    基于OCR识别的文本内容和上下文智能推测颜色

    Args:
        text: OCR识别的文本内容
        layer_name: 当前图层名称
        text_layers: PSD中的文本图层列表，用于参考

    Returns:
        str: 十六进制颜色值
    """
    try:
        print(f"          智能推测OCR颜色: '{text}' (图层: {layer_name})")

        # 方法1: 分析周围PSD文本图层的颜色作为参考
        if text_layers:
            print(f"          参考 {len(text_layers)} 个PSD文本图层...")
            color_frequency = {}

            for psd_layer in text_layers:
                try:
                    # 提取PSD文本图层的颜色
                    psd_color = extract_text_color(psd_layer)
                    if psd_color and psd_color != "#000000":
                        color_frequency[psd_color] = color_frequency.get(psd_color, 0) + 1
                        print(f"            PSD图层 '{psd_layer.name}' 颜色: {psd_color}")
                except:
                    continue

            # 使用最常见的非黑色作为参考
            if color_frequency:
                most_common_color = max(color_frequency.items(), key=lambda x: x[1])[0]
                print(f"          ★基于PSD图层参考，推测颜色: {most_common_color}")
                return most_common_color

        # 方法2: 基于文本内容推测颜色
        content_color_hints = {
            # 佛教相关
            '佛': '#8B4513',         # 棕色，庄重
            '菩萨': '#8B4513',       # 棕色
            '观音': '#8B4513',       # 棕色
            '普陀山': '#8B4513',     # 棕色
            '南无': '#8B4513',       # 棕色
            '阿弥陀': '#8B4513',     # 棕色

            # 时间相关
            '早安': '#FF6B6B',       # 温暖的红色
            '晚安': '#4169E1',       # 蓝色
            '星期': '#4169E1',       # 蓝色
            '农历': '#8B4513',       # 棕色，传统
            '2025': '#333333',       # 深灰色
            '年': '#333333',         # 深灰色
            '月': '#333333',         # 深灰色
            '日': '#333333',         # 深灰色

            # 情感相关
            '善念': '#32CD32',       # 绿色，善良
            '心存': '#32CD32',       # 绿色
            '世界和阳光': '#FFD700', # 金色，象征阳光
            '阳光': '#FFD700',       # 金色
            '平安': '#32CD32',       # 绿色
            '健康': '#32CD32',       # 绿色
            '快乐': '#FF6B6B',       # 红色
            '幸福': '#FF6B6B',       # 红色
        }

        for hint, color in content_color_hints.items():
            if hint in text:
                print(f"          ★基于文本内容 '{text}' 推测颜色: {color}")
                return color

        # 方法3: 基于图层名称推测颜色
        if layer_name:
            layer_name_lower = layer_name.lower()
            layer_color_hints = {
                '红': '#FF0000', '绿': '#00FF00', '蓝': '#0000FF', '白': '#FFFFFF',
                '黑': '#000000', '黄': '#FFFF00', '紫': '#800080', '橙': '#FFA500',
                'red': '#FF0000', 'green': '#00FF00', 'blue': '#0000FF', 'white': '#FFFFFF',
                'black': '#000000', 'yellow': '#FFFF00', 'purple': '#800080', 'orange': '#FFA500',
                'title': '#333333', 'header': '#333333', 'content': '#666666'
            }

            for hint, color in layer_color_hints.items():
                if hint in layer_name_lower:
                    print(f"          ★基于图层名称 '{layer_name}' 推测颜色: {color}")
                    return color

        # 方法4: 基于文本类型推测颜色
        has_chinese = any('\u4e00' <= c <= '\u9fff' for c in text) if text else False

        if has_chinese:
            # 中文文本默认使用深色
            if any(keyword in text for keyword in ['佛', '禅', '道', '古', '典', '雅']):
                print(f"          ★中文传统文本，推测颜色: #8B4513")
                return '#8B4513'  # 棕色
            else:
                print(f"          ★中文文本，推测颜色: #333333")
                return '#333333'  # 深灰色
        else:
            # 英文或数字文本
            if text.isdigit():
                print(f"          ★数字文本，推测颜色: #666666")
                return '#666666'  # 中灰色
            else:
                print(f"          ★英文文本，推测颜色: #333333")
                return '#333333'  # 深灰色

    except Exception as e:
        print(f"          OCR颜色推测失败: {e}")
        return "#000000"

def detect_text_orientation_advanced(layer):
    """
    基于PSD图层属性的高级文本排版方向检测

    Args:
        layer: PSD图层对象

    Returns:
        tuple: (orientation, color, font_name, font_size) - 排版方向、颜色、字体名称、字体大小
    """
    width, height = layer.width, layer.height
    text_content = layer.text if hasattr(layer, 'text') else ""

    print(f"        高级排版检测: '{layer.name}'")
    print(f"        文本内容: '{text_content}'")
    print(f"        图层尺寸: {width}x{height}")

    # 1. 检查图层变换（旋转）
    has_rotation = False
    rotation_angle = 0

    try:
        if hasattr(layer, 'transform') and layer.transform:
            # 检查变换矩阵是否包含旋转
            transform = layer.transform
            if hasattr(transform, 'xx') and hasattr(transform, 'xy'):
                # 简单检测旋转：如果xy或yx不为0，可能有旋转
                if abs(getattr(transform, 'xy', 0)) > 0.01 or abs(getattr(transform, 'yx', 0)) > 0.01:
                    has_rotation = True
                    print(f"        检测到图层旋转")
    except Exception as e:
        print(f"        变换检测失败: {e}")

    # 2. 深度检查文本数据属性
    text_orientation = None
    writing_direction = None

    try:
        # 检查图层的所有属性
        print(f"        图层属性调试:")
        for attr_name in dir(layer):
            if not attr_name.startswith('_') and 'text' in attr_name.lower():
                try:
                    attr_value = getattr(layer, attr_name)
                    print(f"          {attr_name}: {attr_value}")
                except:
                    pass

        if hasattr(layer, 'text_data') and layer.text_data:
            text_data = layer.text_data
            print(f"        text_data类型: {type(text_data)}")

            # 检查text_data的所有属性
            print(f"        text_data属性:")
            for attr_name in dir(text_data):
                if not attr_name.startswith('_'):
                    try:
                        attr_value = getattr(text_data, attr_name)
                        print(f"          {attr_name}: {attr_value}")

                        # 特别关注方向相关的属性
                        if any(keyword in attr_name.lower() for keyword in ['orient', 'direct', 'vertical', 'horizontal']):
                            if attr_name == 'orientation':
                                text_orientation = attr_value
                            elif 'direction' in attr_name:
                                writing_direction = attr_value
                    except Exception as e:
                        print(f"          {attr_name}: 无法访问 ({e})")

        # 检查是否有其他文本相关的属性
        if hasattr(layer, 'engine_data'):
            print(f"        检查engine_data...")
            try:
                engine_data = layer.engine_data
                print(f"        engine_data: {engine_data}")
            except Exception as e:
                print(f"        engine_data访问失败: {e}")

    except Exception as e:
        print(f"        文本属性检测失败: {e}")

    # 3. 获取text_type属性
    text_type = None
    try:
        if hasattr(layer, 'text_type'):
            text_type = layer.text_type
            print(f"        发现text_type: {text_type}")
    except Exception as e:
        print(f"        text_type获取失败: {e}")

    # 4. 提取文本颜色
    text_color = extract_text_color(layer)

    # 5. 提取字体信息
    font_name, font_size = extract_text_font_info(layer)

    # 6. 基于您的逻辑进行判断
    orientation = determine_orientation_by_rules(
        width, height, text_content, has_rotation, text_orientation, writing_direction, text_type
    )

    print(f"        最终判断: {orientation}, 颜色: {text_color}, 字体: {font_name} {font_size}pt")
    return orientation, text_color, font_name, font_size

def determine_orientation_by_rules(width, height, text_content, has_rotation, text_orientation, writing_direction, text_type=None):
    """
    根据您提供的规则判断文本方向
    """
    aspect_ratio = width / height if height > 0 else 1

    # 规则0: 结合text_type和边界框尺寸判断
    if text_type is not None:
        print(f"          text_type分析: {text_type}")
        print(f"          边界框分析: {width}x{height}, 宽高比: {aspect_ratio:.2f}")

        if text_type == 1:
            # text_type=1 可能表示特殊文本，需要结合边界框判断
            if aspect_ratio > 2.0:
                print(f"          text_type=1 但宽高比>2.0，判断为横排")
                return 'horizontal'
            else:
                print(f"          text_type=1 且宽高比适中，判断为竖排")
                return 'vertical'
        elif text_type == 0:
            # text_type=0 通常是横排文本
            print(f"          text_type=0，判断为横排文本")
            return 'horizontal'

    # 规则1: 如果检测到明确的文本方向属性
    if text_orientation:
        if 'vertical' in str(text_orientation).lower():
            return 'vertical'
        elif 'horizontal' in str(text_orientation).lower():
            return 'horizontal'

    # 规则2: 如果检测到书写方向
    if writing_direction:
        if 'vertical' in str(writing_direction).lower():
            return 'vertical'
        elif 'ltr' in str(writing_direction).lower() or 'horizontal' in str(writing_direction).lower():
            return 'horizontal'

    # 规则3: 如果检测到旋转，可能是竖排
    if has_rotation:
        return 'vertical'

    # 规则4: 基于边界框尺寸判断（您的核心逻辑）
    if width > height:
        # 横排文字图层：boundingBox.width > height
        return 'horizontal'
    elif height > width:
        # 竖排文字图层：boundingBox.height > width
        return 'vertical'
    else:
        # 宽高相等的情况，需要更细致判断
        return determine_orientation_fallback(width, height, text_content)

def determine_orientation_fallback(width, height, text_content):
    """
    宽高比接近1:1时的备用判断逻辑
    """
    has_chinese = any('\u4e00' <= c <= '\u9fff' for c in text_content) if text_content else False
    text_length = len(text_content.strip()) if text_content else 0

    if has_chinese:
        # 中文文本的特殊处理
        has_punctuation = any(c in '，。、；：！？' for c in text_content)

        # 长文本且包含标点，倾向竖排
        if text_length >= 8 and has_punctuation:
            return 'vertical'
        # 短文本，倾向竖排
        elif text_length <= 3:
            return 'vertical'
        else:
            return 'horizontal'
    else:
        # 英文文本默认横排
        return 'horizontal'

def detect_text_orientation(width, height, text_content=""):
    """
    保持向后兼容的简单版本
    """
    # 基本的宽高比判断
    aspect_ratio = width / height if height > 0 else 1

    print(f"        文本区域尺寸: {width}x{height}, 宽高比: {aspect_ratio:.2f}")
    print(f"        文本内容: '{text_content}'")

    # 检查是否包含中文
    has_chinese = any('\u4e00' <= c <= '\u9fff' for c in text_content) if text_content else False
    text_length = len(text_content.strip()) if text_content else 0

    # 修正的判断逻辑（更多依赖宽高比）
    if text_content and has_chinese:
        # 中文文本的判断逻辑

        # 检查是否包含标点符号（可能表示句子结构）
        has_punctuation = any(c in '，。、；：！？' for c in text_content)

        # 首先检查宽高比（优先级最高）
        if aspect_ratio > 2.0:
            # 宽度明显大于高度，判断为横排
            orientation = 'horizontal'
            reason = f"宽高比 {aspect_ratio:.2f} > 2.0，判断为横排"

        elif aspect_ratio < 0.5:
            # 高度明显大于宽度，判断为竖排
            orientation = 'vertical'
            reason = f"宽高比 {aspect_ratio:.2f} < 0.5，判断为竖排"

        # 中等宽高比的情况，需要更细致判断
        else:
            # 长文本且包含标点，且高度>宽度，可能是竖排
            if text_length >= 10 and has_punctuation and height > width:
                orientation = 'vertical'
                reason = f"中文长文本({text_length}字)且包含标点，高度>宽度，判断为竖排"

            # 短文本且高度明显大于宽度
            elif text_length <= 3 and aspect_ratio < 0.8:
                orientation = 'vertical'
                reason = f"中文短文本({text_length}字)且高度明显大于宽度，判断为竖排"

            # 其他情况根据宽高比判断
            elif aspect_ratio >= 1.0:
                orientation = 'horizontal'
                reason = f"宽度≥高度，判断为横排"
            else:
                orientation = 'vertical'
                reason = f"高度>宽度，判断为竖排"

    else:
        # 英文或无文本的判断逻辑
        if aspect_ratio > 2.0:
            orientation = 'horizontal'
            reason = f"宽高比 {aspect_ratio:.2f} > 2.0，判断为横排"
        elif aspect_ratio < 0.5:
            orientation = 'vertical'
            reason = f"宽高比 {aspect_ratio:.2f} < 0.5，判断为竖排"
        else:
            # 根据宽高比判断，而不是默认横排
            if height > width:
                orientation = 'vertical'
                reason = f"英文文本或无内容，高度>宽度，判断为竖排"
            else:
                orientation = 'horizontal'
                reason = f"英文文本或无内容，宽度≥高度，判断为横排"

    print(f"        排版方向判断: {orientation} ({reason})")
    return orientation

def process_psd_file(task_id, psd_path, output_folder):
    """处理PSD文件的后台任务"""
    try:
        tasks[task_id]['status'] = 'processing'
        tasks[task_id]['progress'] = 10
        tasks[task_id]['message'] = '正在解析PSD文件...'
        
        base_name = os.path.splitext(os.path.basename(psd_path))[0]
        psd_output_folder = os.path.join(output_folder, base_name)
        os.makedirs(psd_output_folder, exist_ok=True)
        
        # 初始化OCR处理器
        ocr_processor = WebOCRProcessor()
        
        tasks[task_id]['progress'] = 20
        tasks[task_id]['message'] = '正在读取PSD图层...'
        
        # 打开PSD文件
        psd = PSDImage.open(psd_path)
        all_layers = [layer for layer in psd.descendants() if layer.is_visible() and not layer.is_group()]
        
        tasks[task_id]['progress'] = 30
        tasks[task_id]['message'] = '正在分析图层结构...'
        
        # 找背景图层
        background_layer = None
        for layer in all_layers:
            if '背景' in layer.name or 'background' in layer.name.lower():
                background_layer = layer
                break
        
        if not background_layer:
            image_layers = [layer for layer in all_layers if layer.kind != 'type']
            if image_layers:
                background_layer = max(image_layers, key=lambda l: l.width * l.height)
        
        tasks[task_id]['progress'] = 40
        tasks[task_id]['message'] = '正在生成背景图和缩略图...'
        
        # 生成背景图
        background_png = ''
        if background_layer:
            safe_layer_name = "".join([c if c.isalnum() or c in (' ', '_') else '_' for c in background_layer.name])
            background_png = f'{base_name}_background_{safe_layer_name}.png'
            background_path = os.path.join(psd_output_folder, background_png)
            image = background_layer.composite()
            if image:
                image.save(background_path)
        
        # 生成缩略图
        thumbnail_name = f'{base_name}_thumbnail.png'
        thumbnail_path = os.path.join(psd_output_folder, thumbnail_name)
        try:
            thumbnail = psd.composite()
            if thumbnail:
                thumbnail.thumbnail((300, 200))
                thumbnail.save(thumbnail_path)
                thumbnail_url = thumbnail_name
            else:
                thumbnail_url = ''
        except:
            thumbnail_url = ''
        
        tasks[task_id]['progress'] = 50
        tasks[task_id]['message'] = '正在处理文本图层...'
        
        # 生成INI配置内容
        ini_content = f"""[template:0]
name = {base_name}模板
thumbnail = {thumbnail_url}

[template:0:cards:0]
background = {background_png}

"""
        
        text_area_index = 0
        image_area_index = 0
        
        # 处理文本图层
        text_layers = [layer for layer in all_layers if layer.kind == 'type' and layer.is_visible()]
        for i, layer in enumerate(text_layers):
            progress = 50 + (i / len(text_layers)) * 20
            tasks[task_id]['progress'] = int(progress)
            tasks[task_id]['message'] = f'正在处理文本图层: {layer.name}'

            text = layer.text
            left, top = layer.left, layer.top
            width, height = layer.width, layer.height

            print(f"    处理文本图层: '{layer.name}'")
            print(f"      文本内容: '{text}'")

            # 使用高级排版方向检测（基于PSD图层属性）
            orientation, text_color, font_name, font_size = detect_text_orientation_advanced(layer)

            ini_content += f"""[template:0:cards:0:textAreas:{text_area_index}]
content = {text}
color = {text_color}
maxLength = {max(50, len(text) + 20)}
position:x = {left}
position:y = {top}
size:width = {width}
size:height = {height}
font={font_name}
fontSize={font_size}
orientation={orientation}

"""
            text_area_index += 1
            print(f"      -> 已添加文字区域: '{text}' (排版: {orientation})")
        
        tasks[task_id]['progress'] = 70
        tasks[task_id]['message'] = '正在处理图像图层和OCR识别...'
        
        # 处理图像图层
        image_layers = [layer for layer in all_layers if layer.kind != 'type' and not layer.is_group() and layer.is_visible()]
        ocr_count = 0
        
        for i, layer in enumerate(image_layers):
            progress = 70 + (i / len(image_layers)) * 20
            tasks[task_id]['progress'] = int(progress)
            tasks[task_id]['message'] = f'正在处理图像图层: {layer.name}'

            print(f"    处理图层 {i+1}/{len(image_layers)}: '{layer.name}' (尺寸: {layer.width}x{layer.height})")

            if layer.width <= 10 or layer.height <= 10:
                print(f"      -> 跳过: 图层尺寸太小")
                continue

            # 检查是否为背景图层，避免重复处理
            if layer == background_layer:
                print(f"      -> 跳过: 这是背景图层，已单独处理")
                continue

            try:
                image = layer.composite()
                if image:
                    # 使用统一的命名规则：索引_图层名
                    safe_layer_name = "".join([c if c.isalnum() or c in (' ', '_') else '_' for c in layer.name])
                    # 限制文件名长度，避免过长
                    if len(safe_layer_name) > 50:
                        safe_layer_name = safe_layer_name[:50]

                    png_name = f'{base_name}_image_{image_area_index}_{safe_layer_name}.png'
                    png_path = os.path.join(psd_output_folder, png_name)
                    image.save(png_path)
                    left, top = layer.left, layer.top
                    width, height = layer.width, layer.height

                    print(f"      -> 保存图片: {png_name}")

                    # OCR文字识别
                    if ocr_processor.ocr_available:
                        print(f"      -> 进行OCR识别...")
                        ocr_results = ocr_processor.extract_text_from_image(image, layer.name)

                        if ocr_results:
                            print(f"      -> OCR识别到 {len(ocr_results)} 个文字")
                            for result in ocr_results:
                                text = result['text']
                                confidence = result['confidence']
                                bbox = result['bbox']

                                ocr_x = left + bbox[0]
                                ocr_y = top + bbox[1]
                                ocr_w = bbox[2]
                                ocr_h = bbox[3]

                                # 估算字体大小
                                estimated_font_size = max(12, min(48, int(ocr_h * 0.8)))

                                # 智能判断OCR文本的排版方向
                                ocr_orientation = detect_text_orientation(ocr_w, ocr_h, text)

                                # OCR识别的文字使用智能推测颜色和字体
                                ocr_color = "#000000"  # OCR默认黑色
                                ocr_font = estimate_ocr_font(text)  # 基于内容推测字体

                                ini_content += f"""[template:0:cards:0:textAreas:{text_area_index}]
content = {text}
color = {ocr_color}
maxLength = {max(50, len(text) + 20)}
position:x = {ocr_x}
position:y = {ocr_y}
size:width = {ocr_w}
size:height = {ocr_h}
font={ocr_font}
fontSize={estimated_font_size}
orientation={ocr_orientation}
source=OCR识别
confidence={confidence:.3f}

"""
                                text_area_index += 1
                                ocr_count += 1
                                print(f"        - '{text}' (置信度: {confidence:.3f})")
                        else:
                            print(f"      -> OCR未识别到文字")

                    # 添加图像区域配置（现在只处理非背景图层）
                    ini_content += f"""[template:0:cards:0:imageAreas:{image_area_index}]
url = {png_name}
position:x = {left}
position:y = {top}
size:width = {width}
size:height = {height}

"""
                    image_area_index += 1
                    print(f"      -> 已添加图像区域 {image_area_index-1}")

            except Exception as e:
                print(f"      -> 处理失败: {str(e)}")
                continue
        
        tasks[task_id]['progress'] = 90
        tasks[task_id]['message'] = '正在保存配置文件...'
        
        # 保存INI文件
        ini_path = os.path.join(psd_output_folder, f"{base_name}.ini")
        with open(ini_path, 'w', encoding='utf-8') as f:
            f.write(ini_content)
        
        # 创建ZIP文件，使用原始文件名和时间戳
        original_filename = tasks[task_id]['original_filename']
        original_base_name = os.path.splitext(original_filename)[0]

        # 添加高精度时间戳（包含毫秒）
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # 去掉后3位微秒，保留毫秒
        zip_filename = f"{original_base_name}_处理结果_{timestamp}.zip"
        zip_display_name = f"{original_base_name}_处理结果_{timestamp}.zip"
        zip_path = os.path.join(output_folder, zip_filename)

        # 如果文件已存在，添加序号
        counter = 1
        while os.path.exists(zip_path):
            zip_filename = f"{original_base_name}_处理结果_{timestamp}_{counter}.zip"
            zip_display_name = f"{original_base_name}_处理结果_{timestamp}_{counter}.zip"
            zip_path = os.path.join(output_folder, zip_filename)
            counter += 1
            print(f"    -> 检测到文件名冲突，使用序号: {counter-1}")

        with zipfile.ZipFile(zip_path, 'w') as zipf:
            for root, dirs, files in os.walk(psd_output_folder):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, psd_output_folder)
                    zipf.write(file_path, arcname)

        tasks[task_id]['progress'] = 100
        tasks[task_id]['status'] = 'completed'
        tasks[task_id]['message'] = f'文件 "{original_filename}" 处理完成!'
        tasks[task_id]['completed_at'] = datetime.now()
        tasks[task_id]['result'] = {
            'zip_file': zip_filename,
            'zip_display_name': zip_display_name,
            'ini_file': f"{base_name}.ini",
            'image_areas': image_area_index,
            'text_areas': text_area_index,
            'ocr_count': ocr_count,
            'thumbnail': thumbnail_name if thumbnail_url else None,
            'original_filename': original_filename,
            'processed_at': timestamp
        }
        
    except Exception as e:
        tasks[task_id]['status'] = 'error'
        tasks[task_id]['message'] = f'处理失败: {str(e)}'

def process_psd_file_batch(task_id, psd_path, output_folder, batch_id):
    """批量处理PSD文件的后台任务"""
    try:
        # 先处理单个文件
        process_psd_file(task_id, psd_path, output_folder)

        # 更新批次进度
        batch_task = tasks[batch_id]
        batch_task['completed_files'] += 1

        # 计算批次总进度
        total_progress = (batch_task['completed_files'] / batch_task['total_files']) * 100
        batch_task['progress'] = int(total_progress)
        batch_task['message'] = f'已完成 {batch_task["completed_files"]}/{batch_task["total_files"]} 个文件'

        # 检查是否所有文件都处理完成
        if batch_task['completed_files'] >= batch_task['total_files']:
            # 创建批量ZIP文件
            create_batch_zip(batch_id, output_folder)

    except Exception as e:
        tasks[task_id]['status'] = 'error'
        tasks[task_id]['message'] = f'批量处理失败: {str(e)}'

def create_batch_zip(batch_id, output_folder):
    """创建批量处理的ZIP文件"""
    try:
        batch_task = tasks[batch_id]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 创建批量ZIP文件名
        batch_zip_filename = f"批量处理结果_{timestamp}.zip"
        batch_zip_path = os.path.join(output_folder, batch_zip_filename)

        # 用于跟踪重复文件名的计数器
        filename_counter = {}

        with zipfile.ZipFile(batch_zip_path, 'w') as batch_zipf:
            for file_task_id in batch_task['file_tasks']:
                file_task = tasks[file_task_id]
                if file_task['status'] == 'completed' and 'result' in file_task:
                    # 添加单个文件的ZIP到批量ZIP中
                    single_zip_path = os.path.join(output_folder, file_task['result']['zip_file'])
                    if os.path.exists(single_zip_path):
                        # 处理重复文件名
                        original_arcname = file_task['result']['zip_display_name']
                        base_name = os.path.splitext(original_arcname)[0]
                        extension = os.path.splitext(original_arcname)[1]

                        # 检查是否有重复
                        if original_arcname in filename_counter:
                            filename_counter[original_arcname] += 1
                            # 添加序号避免重复
                            arcname = f"{base_name}_{filename_counter[original_arcname]}{extension}"
                            print(f"    -> 检测到重复文件名，重命名为: {arcname}")
                        else:
                            filename_counter[original_arcname] = 0
                            arcname = original_arcname

                        batch_zipf.write(single_zip_path, arcname)
                        print(f"    -> 添加到批量ZIP: {arcname}")

        # 更新批次任务状态
        batch_task['status'] = 'completed'
        batch_task['progress'] = 100
        batch_task['message'] = f'批量处理完成! 共处理 {batch_task["total_files"]} 个文件'
        batch_task['completed_at'] = datetime.now()
        batch_task['result'] = {
            'zip_file': batch_zip_filename,
            'zip_display_name': batch_zip_filename,
            'total_files': batch_task['total_files'],
            'completed_files': batch_task['completed_files'],
            'processed_at': timestamp,
            'file_results': []
        }

        # 收集各文件的处理结果
        for file_task_id in batch_task['file_tasks']:
            file_task = tasks[file_task_id]
            if 'result' in file_task:
                batch_task['result']['file_results'].append({
                    'filename': file_task['original_filename'],
                    'status': file_task['status'],
                    'image_areas': file_task['result'].get('image_areas', 0),
                    'text_areas': file_task['result'].get('text_areas', 0),
                    'ocr_count': file_task['result'].get('ocr_count', 0)
                })

    except Exception as e:
        batch_task['status'] = 'error'
        batch_task['message'] = f'创建批量ZIP失败: {str(e)}'

@app.route('/')
def index():
    """主页"""
    return render_template('index.html', ocr_available=OCR_AVAILABLE, ocr_type=OCR_TYPE)

@app.route('/upload', methods=['POST'])
def upload_file():
    """上传PSD文件（支持单文件和多文件）"""
    if 'file' not in request.files and 'files' not in request.files:
        return jsonify({'error': '没有选择文件'}), 400

    # 处理多文件上传
    files = request.files.getlist('files') if 'files' in request.files else [request.files['file']]

    # 过滤空文件和非PSD文件
    valid_files = []
    for file in files:
        if file and file.filename and file.filename.lower().endswith('.psd'):
            valid_files.append(file)

    if not valid_files:
        return jsonify({'error': '请上传至少一个PSD文件'}), 400

    # 生成批次任务ID
    batch_id = str(uuid.uuid4())
    current_time = datetime.now()

    # 创建批次任务
    batch_task = {
        'status': 'processing',
        'progress': 0,
        'message': f'开始处理 {len(valid_files)} 个文件...',
        'created_at': current_time,
        'batch_id': batch_id,
        'total_files': len(valid_files),
        'completed_files': 0,
        'file_tasks': [],
        'is_batch': True
    }

    tasks[batch_id] = batch_task

    # 为每个文件创建子任务
    for i, file in enumerate(valid_files):
        # 生成子任务ID
        task_id = str(uuid.uuid4())

        # 保留原始文件名
        original_filename = secure_filename(file.filename)

        # 创建任务专用目录
        task_upload_dir = os.path.join(app.config['UPLOAD_FOLDER'], task_id)
        os.makedirs(task_upload_dir, exist_ok=True)

        # 保存文件
        upload_path = os.path.join(task_upload_dir, original_filename)
        file.save(upload_path)

        # 创建子任务
        file_task = {
            'task_id': task_id,
            'status': 'queued',
            'progress': 0,
            'message': '等待处理...',
            'created_at': current_time,
            'filename': original_filename,
            'original_filename': original_filename,
            'batch_id': batch_id,
            'file_index': i + 1
        }

        tasks[task_id] = file_task
        batch_task['file_tasks'].append(task_id)

        # 启动后台处理任务
        thread = threading.Thread(
            target=process_psd_file_batch,
            args=(task_id, upload_path, app.config['OUTPUT_FOLDER'], batch_id)
        )
        thread.daemon = True
        thread.start()

    if len(valid_files) == 1:
        # 单文件上传，返回文件任务ID
        return jsonify({
            'task_id': batch_task['file_tasks'][0],
            'message': f'文件 "{valid_files[0].filename}" 上传成功，开始处理...',
            'filename': valid_files[0].filename,
            'is_batch': False
        })
    else:
        # 多文件上传，返回批次任务ID
        return jsonify({
            'task_id': batch_id,
            'message': f'{len(valid_files)} 个文件上传成功，开始批量处理...',
            'total_files': len(valid_files),
            'is_batch': True
        })

@app.route('/status/<task_id>')
def get_status(task_id):
    """获取任务状态"""
    if task_id not in tasks:
        return jsonify({'error': '任务不存在'}), 404
    
    return jsonify(tasks[task_id])

@app.route('/download/<task_id>')
def download_result(task_id):
    """下载处理结果"""
    if task_id not in tasks:
        return jsonify({'error': '任务不存在'}), 404
    
    task = tasks[task_id]
    if task['status'] != 'completed':
        return jsonify({'error': '任务未完成'}), 400
    
    zip_file = task['result']['zip_file']
    zip_path = os.path.join(app.config['OUTPUT_FOLDER'], zip_file)
    
    if os.path.exists(zip_path):
        return send_file(zip_path, as_attachment=True, download_name=zip_file)
    else:
        return jsonify({'error': '文件不存在'}), 404

@app.route('/cleanup')
def cleanup():
    """清理过期任务和文件"""
    current_time = datetime.now()
    expired_tasks = []

    for task_id, task in tasks.items():
        # 删除1小时前的任务
        if (current_time - task['created_at']).seconds > 3600:
            expired_tasks.append(task_id)

    for task_id in expired_tasks:
        # 删除相关文件
        try:
            # 删除上传的文件目录
            task_upload_dir = os.path.join(app.config['UPLOAD_FOLDER'], task_id)
            if os.path.exists(task_upload_dir):
                shutil.rmtree(task_upload_dir)

            # 删除输出的ZIP文件
            if 'result' in tasks[task_id] and 'zip_file' in tasks[task_id]['result']:
                zip_path = os.path.join(app.config['OUTPUT_FOLDER'], tasks[task_id]['result']['zip_file'])
                if os.path.exists(zip_path):
                    os.remove(zip_path)

            # 删除输出目录
            if 'filename' in tasks[task_id]:
                base_name = os.path.splitext(tasks[task_id]['filename'])[0]
                output_dir = os.path.join(app.config['OUTPUT_FOLDER'], base_name)
                if os.path.exists(output_dir):
                    shutil.rmtree(output_dir)
        except Exception as e:
            print(f"清理任务 {task_id} 时出错: {e}")

        del tasks[task_id]

    return jsonify({
        'message': f'清理了 {len(expired_tasks)} 个过期任务',
        'cleaned_tasks': len(expired_tasks)
    })

if __name__ == '__main__':
    print("🚀 PSD文件处理工具 - Web版本")
    print("=" * 40)
    print(f"OCR功能状态: {OCR_TYPE}")
    print("启动Web服务器...")
    print("访问地址: http://localhost:5000")
    print("=" * 40)

    app.run(debug=True, host='0.0.0.0', port=5000)

def test_psd_extraction():
    """
    测试PSD属性提取功能
    """
    print("=" * 60)
    print("PSD属性提取功能测试")
    print("=" * 60)

    # 测试字体映射
    test_fonts = [
        'Source Han Sans CN Medium',
        'SourceHanSansCN-Medium',
        'Microsoft YaHei',
        'SimSun',
        'Arial',
        'Unknown Font'
    ]

    print("\n1. 字体映射测试:")
    for font in test_fonts:
        mapped = map_font_name(font)
        print(f"  {font} → {mapped}")

    # 测试颜色解析
    test_colors = [
        {'r': 255, 'g': 0, 'b': 0},
        {'r': 0.5, 'g': 0.8, 'b': 0.2},
        [255, 128, 64],
        [0.3, 0.6, 0.9],
        '#FF0000',
        'rgb(255, 0, 0)',
        {'c': 0, 'm': 1, 'y': 1, 'k': 0},
        128
    ]

    print("\n2. 颜色解析测试:")
    for color in test_colors:
        parsed = parse_color_value(color)
        print(f"  {color} → {parsed}")

    # 测试OCR颜色推测
    test_ocr_texts = [
        ('佛佑众生', '佛教图层'),
        ('早安', '问候图层'),
        ('2025年', '日期图层'),
        ('Hello World', 'title'),
        ('123', '数字图层')
    ]

    print("\n3. OCR颜色推测测试:")
    for text, layer_name in test_ocr_texts:
        color = estimate_ocr_color(text, layer_name)
        print(f"  '{text}' (图层: {layer_name}) → {color}")

    # 测试新的engine_dict路径
    print("\n4. engine_dict路径测试:")

    # 模拟一个具有engine_dict结构的图层对象
    class MockLayer:
        def __init__(self, name, engine_dict_data):
            self.name = name
            self.engine_dict = engine_dict_data
            self.width = 100
            self.height = 50
            self.text = "测试文本"

    # 测试数据：模拟Adobe标准的engine_dict结构
    test_engine_dict = {
        'ResourceDict': {
            'FontSet': [
                {'Name': 'Source Han Sans CN Medium'},
                {'Name': 'Microsoft YaHei'},
                {'Name': 'SimSun'}
            ]
        },
        'StyleRun': {
            'RunArray': [
                {
                    'StyleSheet': {
                        'Font': 0,  # 索引到FontSet[0]
                        'FontSize': 60.5
                    }
                }
            ]
        }
    }

    mock_layer = MockLayer("测试图层", test_engine_dict)
    font_name, font_size = extract_text_font_info(mock_layer)
    print(f"  模拟Adobe结构 → 字体: {font_name}, 大小: {font_size}pt")

    # 测试空的engine_dict
    empty_layer = MockLayer("空图层", {})
    font_name2, font_size2 = extract_text_font_info(empty_layer)
    print(f"  空engine_dict → 字体: {font_name2}, 大小: {font_size2}pt (应该返回None)")

    print("\n" + "=" * 60)
    print("测试完成！新的engine_dict路径优先级更高，更准确！")
    print("=" * 60)

if __name__ == '__main__':
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == 'test':
        test_psd_extraction()
    else:
        print("🚀 PSD文件处理工具 - Web版本 (增强版)")
        print("=" * 50)
        print(f"OCR功能状态: {OCR_TYPE}")
        print("✨ 新增功能:")
        print("  - 增强PSD文本属性提取 (颜色、字体、字体大小)")
        print("  - 智能OCR颜色推测")
        print("  - 支持更多字体映射")
        print("  - 深度解析StyleSheetData")
        print("启动Web服务器...")
        print("访问地址: http://localhost:5000")
        print("测试命令: python web_app.py test")
        print("=" * 50)

        app.run(debug=True, host='0.0.0.0', port=5000)
