<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PSD文件处理工具 - Web版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .status-bar {
            background: #f8f9fa;
            padding: 15px 30px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .status-item {
            display: inline-block;
            margin-right: 20px;
            padding: 5px 15px;
            background: #e3f2fd;
            border-radius: 20px;
            font-size: 0.9em;
        }
        
        .status-item.available {
            background: #e8f5e8;
            color: #2e7d32;
        }
        
        .status-item.unavailable {
            background: #ffebee;
            color: #c62828;
        }
        
        .main-content {
            padding: 40px;
        }
        
        .upload-area {
            border: 3px dashed #ddd;
            border-radius: 10px;
            padding: 60px 20px;
            text-align: center;
            margin-bottom: 30px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: #4facfe;
            background: #f8f9ff;
        }
        
        .upload-area.dragover {
            border-color: #4facfe;
            background: #e3f2fd;
        }
        
        .upload-icon {
            font-size: 4em;
            color: #ddd;
            margin-bottom: 20px;
        }
        
        .upload-text {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 15px;
        }
        
        .upload-hint {
            color: #999;
            font-size: 0.9em;
        }
        
        .file-input {
            display: none;
        }
        
        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .progress-container {
            display: none;
            margin-top: 30px;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 15px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 10px;
        }
        
        .progress-text {
            text-align: center;
            color: #666;
            font-size: 0.9em;
        }
        
        .result-container {
            display: none;
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .result-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #4facfe;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9em;
            margin-top: 5px;
        }
        
        .download-btn {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            width: 100%;
            padding: 15px;
            font-size: 1.1em;
        }
        
        .error-message {
            background: #ffebee;
            color: #c62828;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            display: none;
        }
        
        .features {
            margin-top: 40px;
            padding-top: 30px;
            border-top: 1px solid #eee;
        }
        
        .features h3 {
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .feature-item {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .feature-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .feature-title {
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
        }
        
        .feature-desc {
            color: #666;
            font-size: 0.9em;
        }
        
        @media (max-width: 600px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .main-content {
                padding: 20px;
            }
            
            .upload-area {
                padding: 40px 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 PSD文件处理工具</h1>
            <p>在线PSD文件解析、文本识别、图像提取和OCR文字识别</p>
        </div>
        
        <div class="status-bar">
            <div class="status-item">
                📄 支持格式: PSD
            </div>
            <div class="status-item {{ 'available' if ocr_available else 'unavailable' }}">
                🔤 OCR功能: {{ ocr_type }}
            </div>
            <div class="status-item available">
                🌐 Web版本: 在线处理
            </div>
        </div>
        
        <div class="main-content">
            <div class="upload-area" id="uploadArea">
                <div class="upload-icon">📁</div>
                <div class="upload-text">点击选择PSD文件或拖拽文件到此处</div>
                <div class="upload-hint">支持单文件或多文件上传，每个文件最大100MB</div>
                <input type="file" id="fileInput" class="file-input" accept=".psd" multiple />
                <div style="margin-top: 15px;">
                    <button class="btn" onclick="document.getElementById('fileInput').click()">
                        📄 选择单个文件
                    </button>
                    <button class="btn" onclick="selectMultipleFiles()">
                        📚 选择多个文件
                    </button>
                </div>
            </div>
            
            <div class="progress-container" id="progressContainer">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text" id="progressText">准备处理...</div>
            </div>
            
            <div class="result-container" id="resultContainer">
                <div class="result-stats" id="resultStats">
                    <!-- 统计信息将在这里显示 -->
                </div>
                <button class="btn download-btn" id="downloadBtn">
                    📥 下载处理结果
                </button>
            </div>
            
            <div class="error-message" id="errorMessage">
                <!-- 错误信息将在这里显示 -->
            </div>
            
            <div class="features">
                <h3>✨ 功能特点</h3>
                <div class="feature-grid">
                    <div class="feature-item">
                        <div class="feature-icon">📋</div>
                        <div class="feature-title">PSD解析</div>
                        <div class="feature-desc">自动识别图层结构和文本内容</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">🔤</div>
                        <div class="feature-title">OCR识别</div>
                        <div class="feature-desc">智能识别图片中的中英文文字</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">🖼️</div>
                        <div class="feature-title">图像提取</div>
                        <div class="feature-desc">自动分离和保存图像图层</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">⚙️</div>
                        <div class="feature-title">配置生成</div>
                        <div class="feature-desc">生成标准格式的INI配置文件</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentTaskId = null;
        let pollInterval = null;

        // 文件上传处理
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        const progressContainer = document.getElementById('progressContainer');
        const resultContainer = document.getElementById('resultContainer');
        const errorMessage = document.getElementById('errorMessage');

        // 拖拽上传
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFiles(Array.from(e.target.files));
            }
        });

        function selectMultipleFiles() {
            const input = document.getElementById('fileInput');
            input.multiple = true;
            input.click();
        }

        function handleFiles(files) {
            // 过滤PSD文件
            const psdFiles = files.filter(file => file.name.toLowerCase().endsWith('.psd'));

            if (psdFiles.length === 0) {
                showError('请选择至少一个PSD文件');
                return;
            }

            // 检查文件大小
            const oversizedFiles = psdFiles.filter(file => file.size > 100 * 1024 * 1024);
            if (oversizedFiles.length > 0) {
                showError(`以下文件超过100MB限制: ${oversizedFiles.map(f => f.name).join(', ')}`);
                return;
            }

            if (psdFiles.length === 1) {
                uploadFile(psdFiles[0]);
            } else {
                uploadMultipleFiles(psdFiles);
            }
        }

        function handleFile(file) {
            handleFiles([file]);
        }

        function uploadFile(file) {
            const formData = new FormData();
            formData.append('file', file);

            hideError();
            showProgress();

            // 显示正在上传的文件名
            updateProgress(0, `正在上传文件: ${file.name}`);

            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showError(data.error);
                    hideProgress();
                } else {
                    currentTaskId = data.task_id;
                    updateProgress(5, `文件 "${data.filename}" 上传成功，开始处理...`);
                    startPolling();
                }
            })
            .catch(error => {
                showError('上传失败: ' + error.message);
                hideProgress();
            });
        }

        function uploadMultipleFiles(files) {
            const formData = new FormData();

            // 添加所有文件到FormData
            files.forEach(file => {
                formData.append('files', file);
            });

            hideError();
            showProgress();

            // 显示正在上传的文件列表
            const fileNames = files.map(f => f.name).join(', ');
            updateProgress(0, `正在上传 ${files.length} 个文件: ${fileNames}`);

            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showError(data.error);
                    hideProgress();
                } else {
                    currentTaskId = data.task_id;
                    if (data.is_batch) {
                        updateProgress(5, `${data.total_files} 个文件上传成功，开始批量处理...`);
                    } else {
                        updateProgress(5, `文件 "${data.filename}" 上传成功，开始处理...`);
                    }
                    startPolling();
                }
            })
            .catch(error => {
                showError('批量上传失败: ' + error.message);
                hideProgress();
            });
        }

        function startPolling() {
            pollInterval = setInterval(() => {
                fetch(`/status/${currentTaskId}`)
                .then(response => response.json())
                .then(data => {
                    updateProgress(data.progress, data.message);
                    
                    if (data.status === 'completed') {
                        clearInterval(pollInterval);
                        showResult(data.result);
                    } else if (data.status === 'error') {
                        clearInterval(pollInterval);
                        showError(data.message);
                        hideProgress();
                    }
                })
                .catch(error => {
                    clearInterval(pollInterval);
                    showError('获取状态失败: ' + error.message);
                    hideProgress();
                });
            }, 1000);
        }

        function updateProgress(progress, message) {
            document.getElementById('progressFill').style.width = progress + '%';
            document.getElementById('progressText').textContent = message;
        }

        function showProgress() {
            progressContainer.style.display = 'block';
            resultContainer.style.display = 'none';
        }

        function hideProgress() {
            progressContainer.style.display = 'none';
        }

        function showResult(result) {
            hideProgress();

            let statsHtml = '';
            let downloadFileName = result.zip_display_name || result.zip_file;

            if (result.total_files) {
                // 批量处理结果
                statsHtml = `
                    <div style="text-align: center; margin-bottom: 20px; padding: 15px; background: #e8f5e8; border-radius: 8px;">
                        <h4 style="margin: 0; color: #2e7d32;">✅ 批量处理完成</h4>
                        <p style="margin: 5px 0 0 0; color: #666;">处理时间: <strong>${result.processed_at}</strong></p>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">${result.total_files}</div>
                        <div class="stat-label">处理文件数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">${result.completed_files}</div>
                        <div class="stat-label">成功文件数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">${result.file_results ? result.file_results.reduce((sum, f) => sum + f.image_areas, 0) : 0}</div>
                        <div class="stat-label">总图像区域</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">${result.file_results ? result.file_results.reduce((sum, f) => sum + f.text_areas, 0) : 0}</div>
                        <div class="stat-label">总文本区域</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">${result.file_results ? result.file_results.reduce((sum, f) => sum + f.ocr_count, 0) : 0}</div>
                        <div class="stat-label">总OCR识别</div>
                    </div>
                `;

                // 显示各文件详情
                if (result.file_results && result.file_results.length > 0) {
                    statsHtml += `
                        <div style="grid-column: 1 / -1; margin-top: 20px;">
                            <h5 style="margin-bottom: 10px; color: #333;">📋 文件处理详情:</h5>
                            <div style="max-height: 200px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 5px;">
                    `;

                    result.file_results.forEach(file => {
                        statsHtml += `
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 5px 0; border-bottom: 1px solid #eee;">
                                <span style="font-weight: bold;">${file.filename}</span>
                                <span style="color: #666; font-size: 0.9em;">
                                    图像:${file.image_areas} | 文本:${file.text_areas} | OCR:${file.ocr_count}
                                </span>
                            </div>
                        `;
                    });

                    statsHtml += `
                            </div>
                        </div>
                    `;
                }
            } else {
                // 单文件处理结果
                const originalFileName = result.original_filename || '未知文件';
                const processedTime = result.processed_at || '未知时间';

                statsHtml = `
                    <div style="text-align: center; margin-bottom: 20px; padding: 15px; background: #e8f5e8; border-radius: 8px;">
                        <h4 style="margin: 0; color: #2e7d32;">✅ 处理完成</h4>
                        <p style="margin: 5px 0 0 0; color: #666;">原始文件: <strong>${originalFileName}</strong></p>
                        <p style="margin: 5px 0 0 0; color: #666;">处理时间: <strong>${processedTime}</strong></p>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">${result.image_areas || 0}</div>
                        <div class="stat-label">图像区域</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">${result.text_areas || 0}</div>
                        <div class="stat-label">文本区域</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">${result.ocr_count || 0}</div>
                        <div class="stat-label">OCR识别</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">1</div>
                        <div class="stat-label">配置文件</div>
                    </div>
                `;
            }

            document.getElementById('resultStats').innerHTML = statsHtml;

            // 更新下载按钮文本
            const downloadBtn = document.getElementById('downloadBtn');
            downloadBtn.textContent = `📥 下载 ${downloadFileName}`;
            downloadBtn.onclick = () => downloadResult();

            resultContainer.style.display = 'block';
        }

        function downloadResult() {
            window.location.href = `/download/${currentTaskId}`;
        }

        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
        }

        function hideError() {
            errorMessage.style.display = 'none';
        }
    </script>
</body>
</html>
