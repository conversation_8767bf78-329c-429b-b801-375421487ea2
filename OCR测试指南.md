# 🔤 OCR功能测试指南

## 📋 问题分析

从您的日志可以看出，OCR功能已经启用（显示"✅ Tesseract"），但在**分析阶段**只是预览PSD结构，**OCR识别是在"生成INI配置"阶段才会执行**。

## 🚀 正确的测试步骤

### 步骤1：点击"生成INI配置"按钮
在GUI中点击"⚙️ 生成INI配置"按钮，这时才会对图像图层进行OCR识别。

### 步骤2：观察OCR识别日志
您应该会看到类似这样的详细日志：

```
🔍 处理卡片 0 '未分组图层' 的图层 (共 4 个):
  检查图像图层: '背景' (类型: pixel, 尺寸: 1104x1472)
    -> 🔍 正在进行OCR文字识别...
    -> 📊 图像信息: (1104, 1472), 模式: RGBA
    -> 💾 调试图像已保存: output/未标题-1/debug_ocr_背景.png
    -> 🎯 OCR识别到 X 个文字区域:
      1. '普陀山' (置信度: 0.85, 位置: (x, y, w, h))
      2. '观音' (置信度: 0.78, 位置: (x, y, w, h))
    -> ✅ 已处理背景图层: 背景 -> 未标题-1_image_0_背景.png (OCR识别: 2个文字)

  检查图像图层: 'logo' (类型: pixel, 尺寸: 120x173)
    -> 🔍 正在进行OCR文字识别...
    -> 📊 图像信息: (120, 173), 模式: RGBA
    -> 💾 调试图像已保存: output/未标题-1/debug_ocr_logo.png
    -> ℹ️ 未识别到文字内容
    -> ✅ 已处理图片图层: logo -> 未标题-1_image_1_logo.png
```

### 步骤3：检查生成的文件
在输出目录中会生成：
- `debug_ocr_背景.png` - 背景图层的调试图像
- `debug_ocr_logo.png` - logo图层的调试图像
- `未标题-1.ini` - 包含OCR识别文字的配置文件

## 🔧 如果OCR没有识别到文字

### 方法1：运行快速测试
```bash
python quick_ocr_test.py
```

这个脚本会自动测试生成的调试图像，使用多种OCR配置。

### 方法2：手动检查调试图像
1. 打开 `output/未标题-1/debug_ocr_背景.png`
2. 查看图像中是否有清晰的文字
3. 如果文字模糊或对比度低，OCR可能无法识别

### 方法3：调整OCR参数
如果图像质量好但仍无法识别，可能需要：
- 降低置信度阈值
- 尝试不同的OCR配置
- 使用EasyOCR替代Tesseract

## 🎯 预期结果

### 成功识别的情况
```ini
# 生成的INI文件应该包含：

# PSD文本图层
[template:0:cards:0:textAreas:0]
content = 早安
source=PSD文本图层

[template:0:cards:0:textAreas:1]
content = 心存善念 佛佑众生
source=PSD文本图层

# OCR识别的文字
[template:0:cards:0:textAreas:2]
content = 普陀山
source=OCR识别
confidence=0.85

[template:0:cards:0:textAreas:3]
content = 观音
source=OCR识别
confidence=0.78
```

### 统计信息
```
📊 生成统计:
  - 卡片数量: 1
  - 图像区域: 1 个
  - 文本区域: 4 个
    └─ PSD文本图层: 2 个
    └─ OCR识别文字: 2 个
  - 背景图片: 1 个
  - 缩略图: 1 个
🔤 OCR功能: 已启用 (Tesseract)
```

## 🐛 常见问题排除

### 问题1：OCR库未正确安装
**现象**：显示"OCR功能状态: ❌ 未安装"
**解决**：运行 `python install_ocr_dependencies.py`

### 问题2：图像质量问题
**现象**：OCR识别不到文字
**解决**：
1. 检查调试图像是否清晰
2. 确保文字与背景对比度足够
3. 文字大小不能太小（建议>12像素）

### 问题3：中文识别问题
**现象**：只识别英文，不识别中文
**解决**：
1. 确保安装了中文语言包
2. 使用EasyOCR（中文支持更好）
3. 检查Tesseract配置

### 问题4：识别结果不准确
**现象**：识别到错误的文字
**解决**：
1. 提高置信度阈值
2. 使用图像预处理
3. 尝试不同的OCR引擎

## 💡 优化建议

### 提高识别准确率
1. **图像质量**：确保文字清晰、对比度高
2. **字体选择**：标准字体识别效果更好
3. **图像大小**：适当的分辨率有助于识别
4. **背景简洁**：避免复杂背景干扰

### 性能优化
1. **使用EasyOCR**：中文识别效果更好
2. **GPU加速**：如果有NVIDIA显卡可开启GPU
3. **批量处理**：大量文件建议分批处理

## 🔄 下一步操作

1. **立即测试**：点击"生成INI配置"按钮
2. **查看日志**：观察OCR识别过程
3. **检查文件**：查看生成的调试图像和INI文件
4. **运行测试**：如有问题运行 `python quick_ocr_test.py`
5. **反馈结果**：告诉我OCR识别的具体结果

## 📞 需要帮助？

如果按照以上步骤仍然无法识别到"普陀山"等文字，请：

1. 分享OCR识别的完整日志
2. 提供生成的调试图像
3. 告诉我具体的错误信息

我会根据具体情况进一步优化OCR参数和算法！
