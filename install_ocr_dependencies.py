#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
OCR依赖安装脚本
自动安装OCR相关的Python库以启用图片文字识别功能
"""

import subprocess
import sys
import os

def run_command(command):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, encoding='utf-8')
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def install_package(package_name, description=""):
    """安装Python包"""
    print(f"\n📦 正在安装 {package_name}...")
    if description:
        print(f"   {description}")
    
    success, stdout, stderr = run_command(f"{sys.executable} -m pip install {package_name}")
    
    if success:
        print(f"✅ {package_name} 安装成功!")
        return True
    else:
        print(f"❌ {package_name} 安装失败:")
        print(f"   错误信息: {stderr}")
        return False

def check_package(package_name):
    """检查包是否已安装"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False

def main():
    print("🔤 PSD文件处理工具 - OCR依赖安装脚本")
    print("=" * 50)
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"🐍 Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 7):
        print("❌ 错误: 需要Python 3.7或更高版本")
        return False
    
    # 升级pip
    print("\n🔧 升级pip...")
    run_command(f"{sys.executable} -m pip install --upgrade pip")
    
    # 安装基础依赖
    basic_packages = [
        ("opencv-python", "OpenCV - 图像处理库"),
        ("pillow", "PIL - Python图像库"),
        ("numpy", "NumPy - 数值计算库")
    ]
    
    print("\n📚 安装基础依赖...")
    for package, desc in basic_packages:
        if not check_package(package.replace('-', '_')):
            install_package(package, desc)
        else:
            print(f"✅ {package} 已安装")
    
    # 安装OCR库
    print("\n🔤 安装OCR库...")
    
    # 优先安装EasyOCR（推荐）
    easyocr_installed = False
    if not check_package('easyocr'):
        print("\n🎯 安装EasyOCR (推荐)...")
        print("   - 支持中文识别效果更好")
        print("   - 无需额外配置")
        print("   - 首次使用会自动下载模型文件")
        
        if install_package("easyocr", "EasyOCR - 易用的OCR库"):
            easyocr_installed = True
    else:
        print("✅ EasyOCR 已安装")
        easyocr_installed = True
    
    # 备选安装Tesseract
    tesseract_installed = False
    if not easyocr_installed:
        print("\n🔄 EasyOCR安装失败，尝试安装Tesseract...")
        
        if not check_package('pytesseract'):
            if install_package("pytesseract", "PyTesseract - Tesseract OCR的Python接口"):
                tesseract_installed = True
                print("\n⚠️ 注意: Tesseract需要额外安装系统组件:")
                print("   Windows: 下载安装 https://github.com/UB-Mannheim/tesseract/wiki")
                print("   macOS: brew install tesseract")
                print("   Ubuntu: sudo apt install tesseract-ocr tesseract-ocr-chi-sim")
        else:
            print("✅ PyTesseract 已安装")
            tesseract_installed = True
    
    # 测试OCR功能
    print("\n🧪 测试OCR功能...")
    
    if easyocr_installed:
        try:
            import easyocr
            print("✅ EasyOCR 导入成功")
            print("   正在初始化OCR引擎...")
            reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)
            print("✅ EasyOCR 初始化成功!")
        except Exception as e:
            print(f"❌ EasyOCR 测试失败: {e}")
            easyocr_installed = False
    
    if not easyocr_installed and tesseract_installed:
        try:
            import pytesseract
            print("✅ PyTesseract 导入成功")
            # 简单测试（不需要实际图像）
            print("✅ PyTesseract 可用")
        except Exception as e:
            print(f"❌ PyTesseract 测试失败: {e}")
            tesseract_installed = False
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 安装总结:")
    
    if easyocr_installed:
        print("✅ OCR功能: EasyOCR 已就绪")
        print("   - 支持中英文识别")
        print("   - 识别效果优秀")
        print("   - 无需额外配置")
    elif tesseract_installed:
        print("✅ OCR功能: Tesseract 已安装")
        print("   - 需要确保系统已安装Tesseract")
        print("   - 支持多语言识别")
    else:
        print("❌ OCR功能: 安装失败")
        print("   - 图片文字识别功能将不可用")
        print("   - 仍可使用PSD文本图层识别")
    
    print("\n🚀 现在可以运行PSD处理工具:")
    print("   python psd_gui_tool.py  # GUI版本")
    print("   python 123.py <psd文件> <输出目录>  # 命令行版本")
    
    return easyocr_installed or tesseract_installed

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎉 安装完成! OCR功能已启用")
        else:
            print("\n⚠️ 安装完成，但OCR功能可能不可用")
        
        input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n❌ 安装被用户中断")
    except Exception as e:
        print(f"\n❌ 安装过程中出现错误: {e}")
        input("\n按回车键退出...")
