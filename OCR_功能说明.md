# 🔤 OCR图片文字识别功能说明

## 📋 功能概述

新版本的PSD文件处理工具增加了**OCR（光学字符识别）**功能，可以自动识别图片中的文字内容，大大增强了分层识别能力。

### ✨ 主要特性

- 🎯 **智能文字识别**: 自动识别图像图层中的中英文文字
- 📍 **精确定位**: 获取文字在图像中的准确位置和大小
- 🔍 **置信度评估**: 提供文字识别的可信度评分
- 🎨 **自动配置生成**: 为识别的文字自动创建文本区域配置
- 🌐 **多语言支持**: 支持中文简体、英文等多种语言

## 🛠️ 安装OCR依赖

### 方法一：自动安装（推荐）
```bash
python install_ocr_dependencies.py
```

### 方法二：手动安装
```bash
# 安装EasyOCR（推荐）
pip install easyocr opencv-python pillow numpy

# 或者安装Tesseract
pip install pytesseract opencv-python pillow numpy
```

## 📚 支持的OCR引擎

### 1. EasyOCR（推荐）
- ✅ **优点**: 识别准确率高，支持中文效果好，无需额外配置
- ⚠️ **注意**: 首次使用会自动下载模型文件（约100MB）
- 🔧 **安装**: `pip install easyocr`

### 2. Tesseract OCR
- ✅ **优点**: 开源免费，支持多种语言
- ⚠️ **注意**: 需要额外安装系统组件
- 🔧 **安装**: 
  - Windows: 下载安装 [Tesseract](https://github.com/UB-Mannheim/tesseract/wiki)
  - macOS: `brew install tesseract`
  - Ubuntu: `sudo apt install tesseract-ocr tesseract-ocr-chi-sim`

## 🚀 使用方法

### GUI版本
1. 运行 `python psd_gui_tool.py`
2. 选择PSD文件
3. 点击"生成INI配置"
4. 工具会自动进行OCR识别并在日志中显示结果

### 命令行版本
```bash
python 123.py <PSD文件路径> <输出目录>
```

## 📊 OCR识别效果

### 适合识别的文字类型
- ✅ **清晰的印刷体文字**
- ✅ **标准字体（宋体、黑体、楷体等）**
- ✅ **对比度高的文字**
- ✅ **横排或竖排文字**
- ✅ **中英文混合文本**

### 识别效果可能较差的情况
- ❌ 手写字体
- ❌ 艺术字体或特殊字体
- ❌ 模糊或低分辨率图像
- ❌ 背景复杂的文字
- ❌ 过小的文字（小于12像素）

## 🔧 配置参数

### EasyOCR配置
- **置信度阈值**: 0.5（可在代码中调整）
- **支持语言**: 中文简体 + 英文
- **GPU加速**: 默认关闭（可开启以提高速度）

### Tesseract配置
- **置信度阈值**: 30（可在代码中调整）
- **字符白名单**: 包含常用中英文字符
- **识别模式**: PSM 6（统一文本块）

## 📝 输出格式

OCR识别的文字会自动添加到INI配置文件中：

```ini
[template:0:cards:0:textAreas:1]
content = 早安
color = #000000
maxLength = 70
position:x = 150
position:y = 200
size:width = 80
size:height = 25
font=楷体
fontSize=20
orientation=horizontal
source=OCR识别
confidence=0.85
```

## 🐛 故障排除

### 1. OCR库导入失败
```
解决方案：
1. 确认已正确安装OCR库
2. 运行 python install_ocr_dependencies.py
3. 检查Python环境和版本（需要3.7+）
```

### 2. EasyOCR首次运行慢
```
原因：首次使用需要下载模型文件
解决方案：耐心等待下载完成，后续使用会很快
```

### 3. Tesseract识别失败
```
解决方案：
1. 确认系统已安装Tesseract
2. 检查Tesseract路径配置
3. 安装中文语言包
```

### 4. 识别准确率低
```
优化建议：
1. 确保图像清晰度足够
2. 提高图像对比度
3. 使用标准字体
4. 调整置信度阈值
```

## 📈 性能优化

### 提高识别速度
- 使用GPU加速（需要CUDA支持）
- 减少图像尺寸
- 只对包含文字的图层进行OCR

### 提高识别准确率
- 图像预处理（去噪、二值化）
- 调整置信度阈值
- 使用合适的OCR引擎

## 🔄 版本更新

### v2.1 新增功能
- ✅ 集成EasyOCR和Tesseract双引擎
- ✅ 自动图像预处理
- ✅ 智能字体大小估算
- ✅ 置信度评估
- ✅ 详细的识别日志

### 未来计划
- 🔮 支持更多OCR引擎
- 🔮 自定义识别参数
- 🔮 批量OCR优化
- 🔮 识别结果手动校正

## 💡 使用建议

1. **首次使用**: 建议先用小图片测试OCR功能
2. **大批量处理**: 建议在性能较好的机器上运行
3. **准确率要求高**: 建议使用EasyOCR引擎
4. **网络环境差**: 建议提前下载EasyOCR模型文件

## 📞 技术支持

如果在使用OCR功能时遇到问题，请：
1. 查看控制台输出的错误信息
2. 确认OCR库安装正确
3. 检查图像质量和格式
4. 尝试不同的OCR引擎
