#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
from psd_tools import PSDImage

def debug_psd_layers(psd_path):
    """详细分析PSD文件中的所有图层，帮助诊断问题"""
    
    if not os.path.exists(psd_path):
        print(f"❌ 文件不存在: {psd_path}")
        return
    
    try:
        print(f"🔍 正在分析PSD文件: {psd_path}")
        psd = PSDImage.open(psd_path)
        
        print(f"📐 PSD尺寸: {psd.width} x {psd.height}")
        print(f"🎨 颜色模式: {psd.color_mode}")
        
        # 获取所有图层（包括隐藏的）
        all_layers = list(psd.descendants())
        visible_layers = [layer for layer in all_layers if layer.is_visible()]
        
        print(f"\n📊 图层统计:")
        print(f"  - 总图层数: {len(all_layers)}")
        print(f"  - 可见图层数: {len(visible_layers)}")
        
        # 查找可能的佛像相关图层
        buddha_keywords = ['普陀山', '观音', '菩萨', '佛', '佛像', 'buddha', 'guanyin']
        buddha_layers = [layer for layer in all_layers if any(keyword in layer.name.lower() for keyword in buddha_keywords)]
        if buddha_layers:
            print(f"\n🎯 找到佛像相关图层:")
            for layer in buddha_layers:
                print(f"  - 名称: '{layer.name}'")
                print(f"  - 类型: {layer.kind}")
                print(f"  - 尺寸: {layer.width} x {layer.height}")
                print(f"  - 位置: ({layer.left}, {layer.top})")
                print(f"  - 可见: {layer.is_visible()}")
                print(f"  - 是组: {layer.is_group()}")
                if hasattr(layer, 'opacity'):
                    print(f"  - 透明度: {layer.opacity}")
                print()
        else:
            print(f"\n❌ 未找到佛像相关图层")

        # 分析图层尺寸，找出可能的主要图像
        large_image_layers = [
            layer for layer in visible_layers
            if not layer.is_group()
            and layer.kind != 'type'
            and layer.width > 100 and layer.height > 100
        ]

        if large_image_layers:
            print(f"\n🖼️ 大尺寸图像图层 (可能包含主要内容):")
            for layer in large_image_layers:
                area = layer.width * layer.height
                print(f"  - '{layer.name}' ({layer.width}x{layer.height}, 面积:{area:,}像素)")

                # 检查是否可能是背景
                is_fullsize = layer.width >= psd.width * 0.8 and layer.height >= psd.height * 0.8
                if is_fullsize:
                    print(f"    📋 可能是背景图层 (接近全尺寸)")
                else:
                    print(f"    🎨 可能是内容图层")
            
        # 显示所有可见图层的详细信息
        print(f"\n📋 所有可见图层详情:")
        for i, layer in enumerate(visible_layers, 1):
            layer_type = "📝文本" if layer.kind == 'type' else ("📁组" if layer.is_group() else "🖼️图像")
            size_info = f"{layer.width}x{layer.height}"
            pos_info = f"({layer.left},{layer.top})"
            
            # 检查图层名称中是否包含关键词
            keywords = ['普陀山', '印章', 'logo', '标签', '标记']
            has_keyword = any(keyword in layer.name for keyword in keywords)
            keyword_mark = " ⭐" if has_keyword else ""
            
            print(f"  {i:2d}. '{layer.name}' ({layer_type}, {size_info}, {pos_info}){keyword_mark}")
            
            # 如果是小尺寸图层，特别标注
            if not layer.is_group() and (layer.width <= 50 or layer.height <= 50):
                print(f"      ⚠️ 小尺寸图层 - 可能是装饰元素")
        
        # 查找可能被忽略的小图层
        small_image_layers = [
            layer for layer in visible_layers 
            if not layer.is_group() 
            and layer.kind != 'type' 
            and (layer.width <= 50 or layer.height <= 50)
        ]
        
        if small_image_layers:
            print(f"\n⚠️ 发现 {len(small_image_layers)} 个小尺寸图像图层:")
            for layer in small_image_layers:
                print(f"  - '{layer.name}' ({layer.width}x{layer.height})")
        
        # 检查图层组结构
        groups = [layer for layer in psd if layer.is_group()]
        if groups:
            print(f"\n📁 图层组结构:")
            for group in groups:
                group_layers = list(group.descendants())
                visible_group_layers = [l for l in group_layers if l.is_visible()]
                print(f"  - '{group.name}' (总计: {len(group_layers)}, 可见: {len(visible_group_layers)})")
                
                # 检查组内是否有普陀山相关图层
                putuoshan_in_group = [l for l in group_layers if '普陀山' in l.name]
                if putuoshan_in_group:
                    print(f"    🎯 组内包含普陀山图层: {[l.name for l in putuoshan_in_group]}")
        
        print(f"\n✅ 分析完成!")
        
    except Exception as e:
        print(f"❌ 分析失败: {str(e)}")

if __name__ == '__main__':
    if len(sys.argv) > 1:
        psd_path = sys.argv[1]
    else:
        # 默认查找当前目录下的PSD文件
        psd_files = [f for f in os.listdir('.') if f.lower().endswith('.psd')]
        if psd_files:
            psd_path = psd_files[0]
            print(f"🔍 自动选择PSD文件: {psd_path}")
        else:
            print("❌ 请提供PSD文件路径作为参数")
            print("用法: python debug_layers.py <psd文件路径>")
            sys.exit(1)
    
    debug_psd_layers(psd_path)
