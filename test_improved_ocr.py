#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试改进后的OCR功能
"""

import os
import sys

def test_improved_ocr():
    """测试改进后的OCR"""
    print("测试改进后的OCR功能")
    print("=" * 40)
    
    # 检查是否有PSD文件
    psd_file = "未标题-1.psd"
    if not os.path.exists(psd_file):
        print(f"错误: 找不到PSD文件 {psd_file}")
        print("请确保PSD文件在当前目录中")
        return
    
    # 运行改进后的处理脚本
    print(f"正在处理: {psd_file}")
    
    import subprocess
    result = subprocess.run([
        sys.executable, "123_safe.py", psd_file, "output_improved"
    ], capture_output=True, text=True, encoding='utf-8', errors='ignore')
    
    if result.returncode == 0:
        print("✅ 处理成功!")
        print("\n处理输出:")
        print(result.stdout)
        
        # 检查生成的INI文件
        ini_file = "output_improved/未标题-1/未标题-1.ini"
        if os.path.exists(ini_file):
            print(f"\n📄 生成的INI文件内容:")
            print("-" * 40)
            with open(ini_file, 'r', encoding='utf-8') as f:
                content = f.read()
                print(content)
            
            # 统计OCR识别结果
            ocr_count = content.count('source=OCR识别')
            print(f"\n📊 OCR识别统计:")
            print(f"  - OCR识别的文字区域: {ocr_count} 个")
            
            if ocr_count == 0:
                print("  ℹ️ 未识别到有效文字，这可能是因为:")
                print("    1. 图像中没有清晰的文字")
                print("    2. 置信度阈值设置过高")
                print("    3. 文字被过滤算法排除")
            else:
                print("  ✅ 识别到有效文字!")
        else:
            print(f"❌ 未找到INI文件: {ini_file}")
    else:
        print("❌ 处理失败!")
        print("错误输出:")
        print(result.stderr)

def compare_results():
    """比较改进前后的结果"""
    print("\n" + "=" * 50)
    print("比较改进前后的结果")
    print("=" * 50)
    
    old_ini = "output/未标题-1/未标题-1.ini"
    new_ini = "output_improved/未标题-1/未标题-1.ini"
    
    if os.path.exists(old_ini) and os.path.exists(new_ini):
        # 读取旧结果
        with open(old_ini, 'r', encoding='utf-8') as f:
            old_content = f.read()
        old_ocr_count = old_content.count('source=OCR识别')
        
        # 读取新结果
        with open(new_ini, 'r', encoding='utf-8') as f:
            new_content = f.read()
        new_ocr_count = new_content.count('source=OCR识别')
        
        print(f"改进前 OCR识别数量: {old_ocr_count}")
        print(f"改进后 OCR识别数量: {new_ocr_count}")
        print(f"减少无效识别: {old_ocr_count - new_ocr_count} 个")
        
        if new_ocr_count < old_ocr_count:
            print("✅ 成功减少了无效识别!")
        elif new_ocr_count == 0:
            print("⚠️ 可能过滤过度，建议调整参数")
        else:
            print("ℹ️ 识别数量未减少")
    else:
        print("无法比较：缺少对比文件")

if __name__ == "__main__":
    try:
        test_improved_ocr()
        compare_results()
        
        print(f"\n{'='*50}")
        print("测试完成!")
        print("\n💡 如果识别效果不理想，可以:")
        print("1. 调整置信度阈值（当前60%）")
        print("2. 修改文本验证规则")
        print("3. 查看调试图像检查图像质量")
        
        input("\n按回车键退出...")
        
    except KeyboardInterrupt:
        print("\n\n❌ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        input("\n按回车键退出...")
