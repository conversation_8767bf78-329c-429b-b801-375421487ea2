#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试简化的文本排版方向判断功能（只有horizontal和vertical）
"""

def test_simple_orientation_logic():
    """测试简化的排版方向判断逻辑"""
    print("🧪 测试简化的排版方向判断逻辑")
    print("=" * 60)
    
    # 模拟简化的detect_text_orientation函数
    def detect_text_orientation(width, height, text_content=""):
        """简化的排版方向判断"""
        aspect_ratio = width / height if height > 0 else 1
        has_chinese = any('\u4e00' <= c <= '\u9fff' for c in text_content) if text_content else False
        text_length = len(text_content.strip()) if text_content else 0
        
        # 修正的判断逻辑（更多依赖宽高比）
        if text_content and has_chinese:
            # 中文文本的判断逻辑
            has_punctuation = any(c in '，。、；：！？' for c in text_content)

            # 首先检查宽高比（优先级最高）
            if aspect_ratio > 2.0:
                return 'horizontal', f"宽高比 {aspect_ratio:.2f} > 2.0，判断为横排"
            elif aspect_ratio < 0.5:
                return 'vertical', f"宽高比 {aspect_ratio:.2f} < 0.5，判断为竖排"
            else:
                # 长文本且包含标点，且高度>宽度，可能是竖排
                if text_length >= 10 and has_punctuation and height > width:
                    return 'vertical', f"中文长文本({text_length}字)且包含标点，高度>宽度，判断为竖排"
                # 短文本且高度明显大于宽度
                elif text_length <= 3 and aspect_ratio < 0.8:
                    return 'vertical', f"中文短文本({text_length}字)且高度明显大于宽度，判断为竖排"
                # 其他情况根据宽高比判断
                elif aspect_ratio >= 1.0:
                    return 'horizontal', f"宽度≥高度，判断为横排"
                else:
                    return 'vertical', f"高度>宽度，判断为竖排"
        else:
            # 英文或无文本的判断逻辑
            if aspect_ratio > 2.0:
                return 'horizontal', f"宽高比 {aspect_ratio:.2f} > 2.0，判断为横排"
            elif aspect_ratio < 0.5:
                return 'vertical', f"宽高比 {aspect_ratio:.2f} < 0.5，判断为竖排"
            else:
                if height > width:
                    return 'vertical', f"英文文本或无内容，高度>宽度，判断为竖排"
                else:
                    return 'horizontal', f"英文文本或无内容，宽度≥高度，判断为横排"
    
    # 测试用例（基于您的实际反馈修正）
    test_cases = [
        # (宽度, 高度, 文本内容, 预期结果, 描述)

        # 基于您图片中的实际场景（修正后）
        (150, 200, "世界和阳光都刚刚醒来，温柔地包裹着你。", "vertical", "红框中的竖排长文本"),
        (401, 191, "早安", "horizontal", "早安文字：宽度>高度，应该是横排"),
        (472, 54, "心存善念 佛佑众生", "horizontal", "心存善念文字：宽度明显>高度，应该是横排"),
        
        # 明显的横排场景
        (400, 50, "世界和阳光都刚刚醒来，温柔地包裹着你。", "horizontal", "宽度明显大于高度的长文本"),
        (300, 80, "早安", "horizontal", "宽度明显大于高度的短文本"),
        
        # 明显的竖排场景
        (50, 300, "世界和阳光都刚刚醒来，温柔地包裹着你。", "vertical", "高度明显大于宽度的长文本"),
        (40, 200, "早安", "vertical", "高度明显大于宽度的短文本"),
        
        # 边界情况
        (120, 100, "早安", "vertical", "中文短文本，宽高比适中，倾向竖排"),
        (150, 120, "测试文本", "vertical", "中文中等文本，倾向竖排"),
        (200, 100, "Hello World", "horizontal", "英文文本，倾向横排"),
        
        # 包含标点的长文本
        (120, 180, "世界和阳光都刚刚醒来，温柔地包裹着你。", "vertical", "包含标点的中文长文本"),
        (100, 150, "心存善念，佛佑众生。", "vertical", "包含标点的中文文本"),
        
        # 无文本内容
        (200, 100, "", "horizontal", "无文本内容，宽度大于高度"),
        (100, 200, "", "vertical", "无文本内容，高度大于宽度"),
    ]
    
    print("📋 测试用例:")
    print("-" * 60)
    
    success_count = 0
    
    for i, (width, height, text, expected, description) in enumerate(test_cases):
        orientation, reason = detect_text_orientation(width, height, text)
        
        result_icon = "✅" if orientation == expected else "❌"
        aspect_ratio = width / height if height > 0 else 1
        
        print(f"{i+1:2d}. {result_icon} {description}")
        print(f"    尺寸: {width}x{height} (宽高比: {aspect_ratio:.2f})")
        print(f"    文本: '{text}'")
        print(f"    判断: {orientation} ({reason})")
        print(f"    预期: {expected}")
        
        if orientation == expected:
            success_count += 1
        
        print()
    
    print(f"📊 测试结果: {success_count}/{len(test_cases)} 通过 ({success_count/len(test_cases)*100:.1f}%)")
    
    return success_count == len(test_cases)

def main():
    """主函数"""
    print("🧪 简化版文本排版方向判断测试")
    print("=" * 70)
    
    # 测试简化的排版方向判断
    test_success = test_simple_orientation_logic()
    
    # 总结
    print("\n" + "=" * 70)
    print("📊 简化版排版方向判断测试结果:")
    print(f"  判断逻辑测试: {'✅ 通过' if test_success else '❌ 失败'}")
    
    if test_success:
        print("\n🎉 所有测试通过！简化版排版方向判断功能正常工作")
        print("\n✨ 简化的orientation值:")
        print("  - horizontal → 横排")
        print("  - vertical   → 竖排")
        print("\n🔍 判断依据:")
        print("  1. 宽高比分析 (极端比例优先)")
        print("  2. 中文文本特征 (长度、标点、密度)")
        print("  3. 默认规则 (中文倾向竖排，英文倾向横排)")
        print("\n📋 您的PSD文件预期结果:")
        print("  - '早安' → vertical")
        print("  - '心存善念 佛佑众生' → vertical")
        print("  - '世界和阳光都刚刚醒来，温柔地包裹着你。' → vertical")
    else:
        print("\n⚠️ 测试失败，排版方向判断功能需要改进")
    
    return test_success

if __name__ == "__main__":
    try:
        success = main()
        input(f"\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        input("\n按回车键退出...")
