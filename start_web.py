#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
PSD处理工具Web版启动脚本
自动检查依赖并启动Web服务器
"""

import os
import sys
import subprocess
import webbrowser
import time

def check_dependencies():
    """检查必要的依赖"""
    required_packages = [
        'flask',
        'psd_tools',
        'PIL',
        'numpy',
        'cv2',
        'pytesseract'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'PIL':
                from PIL import Image
            elif package == 'cv2':
                import cv2
            elif package == 'psd_tools':
                from psd_tools import PSDImage
            else:
                __import__(package)
            print(f"✅ {package} - 已安装")
        except ImportError:
            print(f"❌ {package} - 未安装")
            missing_packages.append(package)
    
    return missing_packages

def check_directories():
    """检查必要的目录"""
    directories = ['templates', 'uploads', 'outputs']
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✅ 创建目录: {directory}")
        else:
            print(f"✅ 目录存在: {directory}")

def check_template():
    """检查模板文件"""
    template_file = 'templates/index.html'
    if os.path.exists(template_file):
        print(f"✅ 模板文件存在: {template_file}")
        return True
    else:
        print(f"❌ 模板文件不存在: {template_file}")
        return False

def install_dependencies():
    """安装缺失的依赖"""
    print("\n🔧 正在安装缺失的依赖...")
    try:
        result = subprocess.run([sys.executable, 'install_web_dependencies.py'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 依赖安装完成")
            return True
        else:
            print(f"❌ 依赖安装失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 安装过程出错: {e}")
        return False

def start_web_server():
    """启动Web服务器"""
    print("\n🚀 启动Web服务器...")
    
    try:
        # 检查web_app.py是否存在
        if not os.path.exists('web_app.py'):
            print("❌ 找不到web_app.py文件")
            return False
        
        # 启动Web应用
        print("正在启动Flask应用...")
        print("=" * 50)
        print("🌐 PSD文件处理工具 - Web版")
        print("📍 访问地址: http://localhost:5000")
        print("⏹️  按 Ctrl+C 停止服务器")
        print("=" * 50)
        
        # 延迟打开浏览器
        def open_browser():
            time.sleep(2)
            try:
                webbrowser.open('http://localhost:5000')
                print("🌐 已自动打开浏览器")
            except:
                print("⚠️ 无法自动打开浏览器，请手动访问: http://localhost:5000")
        
        import threading
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        # 启动Flask应用
        os.system(f"{sys.executable} web_app.py")
        
        return True
        
    except KeyboardInterrupt:
        print("\n\n⏹️ 服务器已停止")
        return True
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 PSD处理工具Web版启动器")
    print("=" * 40)
    
    # 1. 检查Python版本
    python_version = sys.version_info
    print(f"🐍 Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 7):
        print("❌ 错误: 需要Python 3.7或更高版本")
        input("按回车键退出...")
        return False
    
    # 2. 检查目录结构
    print("\n📁 检查目录结构...")
    check_directories()
    
    # 3. 检查模板文件
    print("\n📄 检查模板文件...")
    if not check_template():
        print("❌ 缺少必要的模板文件，请确保项目完整")
        input("按回车键退出...")
        return False
    
    # 4. 检查依赖
    print("\n📦 检查依赖包...")
    missing_packages = check_dependencies()
    
    if missing_packages:
        print(f"\n⚠️ 发现缺失的依赖: {', '.join(missing_packages)}")
        
        # 检查是否有安装脚本
        if os.path.exists('install_web_dependencies.py'):
            choice = input("是否自动安装缺失的依赖? (y/n): ").lower().strip()
            if choice in ['y', 'yes', '是']:
                if not install_dependencies():
                    print("❌ 依赖安装失败，请手动安装")
                    input("按回车键退出...")
                    return False
                
                # 重新检查依赖
                print("\n🔍 重新检查依赖...")
                missing_packages = check_dependencies()
                if missing_packages:
                    print(f"❌ 仍有缺失的依赖: {', '.join(missing_packages)}")
                    input("按回车键退出...")
                    return False
            else:
                print("❌ 请先安装缺失的依赖")
                print("运行: python install_web_dependencies.py")
                input("按回车键退出...")
                return False
        else:
            print("❌ 找不到依赖安装脚本")
            print("请手动安装缺失的依赖")
            input("按回车键退出...")
            return False
    
    # 5. 启动Web服务器
    print("\n✅ 所有检查通过，准备启动Web服务器...")
    return start_web_server()

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n❌ 启动失败")
    except KeyboardInterrupt:
        print("\n\n⏹️ 启动被用户中断")
    except Exception as e:
        print(f"\n❌ 启动过程中出现错误: {e}")
        input("按回车键退出...")
