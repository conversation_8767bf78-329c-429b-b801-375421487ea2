#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复PaddleOCR版本兼容性问题
"""

import subprocess
import sys
import os

def run_command(command):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, encoding='utf-8')
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def uninstall_paddleocr():
    """卸载当前版本的PaddleOCR"""
    print("🔧 卸载当前版本的PaddleOCR...")
    
    packages_to_remove = [
        "paddleocr",
        "paddlepaddle"
    ]
    
    for package in packages_to_remove:
        print(f"   卸载 {package}...")
        success, stdout, stderr = run_command(f"{sys.executable} -m pip uninstall {package} -y")
        if success:
            print(f"   ✅ {package} 卸载成功")
        else:
            print(f"   ⚠️ {package} 卸载失败或未安装")

def install_stable_versions():
    """安装稳定版本的PaddleOCR"""
    print("\n📦 安装稳定版本的PaddleOCR...")
    
    # 使用更稳定的版本组合
    packages = [
        "paddlepaddle==2.4.2",
        "paddleocr==*******"
    ]
    
    for package in packages:
        print(f"\n   安装 {package}...")
        success, stdout, stderr = run_command(f"{sys.executable} -m pip install {package}")
        if success:
            print(f"   ✅ {package} 安装成功")
        else:
            print(f"   ❌ {package} 安装失败")
            print(f"   错误: {stderr}")
            return False
    
    return True

def test_installation():
    """测试安装是否成功"""
    print("\n🧪 测试PaddleOCR安装...")
    
    try:
        from paddleocr import PaddleOCR
        print("   ✅ PaddleOCR 导入成功")
        
        # 使用最简单的配置测试
        print("   🔧 初始化OCR引擎...")
        ocr = PaddleOCR(lang='ch')
        print("   ✅ PaddleOCR 初始化成功")
        
        # 创建简单测试
        from PIL import Image, ImageDraw
        import numpy as np
        
        img = Image.new('RGB', (100, 50), 'white')
        draw = ImageDraw.Draw(img)
        draw.text((10, 10), "测试", fill='black')
        
        img_array = np.array(img)
        result = ocr.ocr(img_array, cls=True)
        
        print("   ✅ OCR功能测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def create_simple_ocr_processor():
    """创建简化的OCR处理器"""
    print("\n📝 创建简化的OCR处理器...")
    
    simple_ocr_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
from psd_tools import PSDImage
import numpy as np
from PIL import Image

# 简化的PaddleOCR处理器
OCR_AVAILABLE = False
OCR_TYPE = "无"

try:
    from paddleocr import PaddleOCR
    OCR_AVAILABLE = True
    OCR_TYPE = "PaddleOCR"
    print("PaddleOCR 可用")
except ImportError:
    print("未安装PaddleOCR")

class SimplePaddleOCRProcessor:
    """简化的PaddleOCR处理器"""
    
    def __init__(self):
        self.ocr_reader = None
        self._init_ocr()
    
    def _init_ocr(self):
        """初始化OCR引擎"""
        if not OCR_AVAILABLE:
            return
            
        try:
            # 使用最简单的配置
            self.ocr_reader = PaddleOCR(lang='ch')
            print("使用 PaddleOCR 引擎")
        except Exception as e:
            print(f"PaddleOCR引擎初始化失败: {e}")
    
    def extract_text_from_image(self, image, layer_name=""):
        """从图像中提取文字"""
        if not OCR_AVAILABLE or not self.ocr_reader:
            return []
        
        try:
            # 将PIL图像转换为numpy数组
            if isinstance(image, Image.Image):
                img_array = np.array(image)
            else:
                img_array = image
            
            # 转换RGBA到RGB
            if len(img_array.shape) == 3 and img_array.shape[2] == 4:
                rgb_array = np.ones((img_array.shape[0], img_array.shape[1], 3), dtype=np.uint8) * 255
                alpha = img_array[:, :, 3:4] / 255.0
                rgb_array = rgb_array * (1 - alpha) + img_array[:, :, :3] * alpha
                img_array = rgb_array.astype(np.uint8)
            
            # 进行OCR识别
            result = self.ocr_reader.ocr(img_array, cls=True)
            
            text_results = []
            
            if result and result[0]:
                print(f"    -> PaddleOCR识别到 {len(result[0])} 个文字区域:")
                
                for i, line in enumerate(result[0]):
                    bbox_points = line[0]
                    text_info = line[1]
                    
                    text = text_info[0].strip()
                    confidence = text_info[1]
                    
                    # 计算边界框
                    x_coords = [point[0] for point in bbox_points]
                    y_coords = [point[1] for point in bbox_points]
                    x = int(min(x_coords))
                    y = int(min(y_coords))
                    w = int(max(x_coords) - min(x_coords))
                    h = int(max(y_coords) - min(y_coords))
                    
                    # 简单过滤：置信度>0.6，文字长度>1
                    if confidence > 0.6 and len(text) > 1 and w > 5 and h > 5:
                        # 检查是否包含中文或有意义的英文
                        has_chinese = any('\\u4e00' <= c <= '\\u9fff' for c in text)
                        has_english = any(c.isalpha() for c in text)
                        
                        if has_chinese or (has_english and len(text) >= 2):
                            print(f"      {i+1}. '{text}' (置信度: {confidence:.2f})")
                            
                            text_results.append({
                                'text': text,
                                'confidence': confidence,
                                'bbox': (x, y, w, h),
                                'source': f'PaddleOCR识别-{layer_name}'
                            })
                        else:
                            print(f"      {i+1}. '{text}' (置信度: {confidence:.2f}) - 已过滤")
                    else:
                        print(f"      {i+1}. '{text}' (置信度: {confidence:.2f}) - 质量过低")
            
            return text_results
            
        except Exception as e:
            print(f"    PaddleOCR识别出错: {str(e)}")
            return []

def rgba_to_hex(rgba):
    if isinstance(rgba, (list, tuple)):
        return '#{:02x}{:02x}{:02x}'.format(rgba[0], rgba[1], rgba[2])
    return '#000000'

def process_psd(psd_path, base_output_folder):
    base_name = os.path.splitext(os.path.basename(psd_path))[0]
    psd_output_folder = os.path.join(base_output_folder, base_name)
    if not os.path.exists(psd_output_folder):
        os.makedirs(psd_output_folder)

    # 初始化OCR处理器
    ocr_processor = SimplePaddleOCRProcessor()
    print(f"OCR功能状态: {'可用' if OCR_AVAILABLE else '不可用'} ({OCR_TYPE})")

    psd = PSDImage.open(psd_path)
    all_layers = [layer for layer in psd.descendants() if layer.is_visible() and not layer.is_group()]

    # 找背景图层
    background_layer = None
    for layer in all_layers:
        if '背景' in layer.name or 'background' in layer.name.lower():
            background_layer = layer
            break
    
    if not background_layer:
        image_layers = [layer for layer in all_layers if layer.kind != 'type']
        if image_layers:
            background_layer = max(image_layers, key=lambda l: l.width * l.height)

    if background_layer:
        print(f"找到背景图层: '{background_layer.name}' ({background_layer.width}x{background_layer.height})")

    # 生成背景图和缩略图
    background_png = ''
    if background_layer:
        safe_layer_name = "".join([c if c.isalnum() or c in (' ', '_') else '_' for c in background_layer.name])
        background_png = f'{base_name}_background_{safe_layer_name}.png'
        background_path = os.path.join(psd_output_folder, background_png)
        image = background_layer.composite()
        if image:
            image.save(background_path)

    thumbnail_name = f'{base_name}_thumbnail.png'
    thumbnail_path = os.path.join(psd_output_folder, thumbnail_name)
    try:
        thumbnail = psd.composite()
        if thumbnail:
            thumbnail.thumbnail((300, 200))
            thumbnail.save(thumbnail_path)
            thumbnail_url = thumbnail_name
        else:
            thumbnail_url = ''
    except:
        thumbnail_url = ''

    # 生成INI内容
    ini_content = f\"\"\"[template:0]
name = {base_name}模板
thumbnail = {thumbnail_url}

[template:0:cards:0]
background = {background_png}

\"\"\"

    text_area_index = 0
    image_area_index = 0

    print(f"\\n处理图层:")

    # 处理文本图层
    text_layers = [layer for layer in all_layers if layer.kind == 'type' and layer.is_visible()]
    for layer in text_layers:
        print(f"  处理文本图层: '{layer.name}'")
        text = layer.text
        left, top = layer.left, layer.top
        width, height = layer.width, layer.height

        ini_content += f\"\"\"[template:0:cards:0:textAreas:{text_area_index}]
content = {text}
color = #000000
maxLength = {max(50, len(text) + 20)}
position:x = {left}
position:y = {top}
size:width = {width}
size:height = {height}
font=楷体
fontSize=20
orientation=horizontal

\"\"\"
        text_area_index += 1
        print(f"    -> 已添加文字区域: '{text}'")

    # 处理图像图层
    image_layers = [layer for layer in all_layers if layer.kind != 'type' and not layer.is_group() and layer.is_visible()]
    for layer in image_layers:
        print(f"  处理图像图层: '{layer.name}' (尺寸: {layer.width}x{layer.height})")

        if layer.width <= 3 or layer.height <= 3:
            print(f"    -> 跳过: 图层尺寸太小")
            continue

        try:
            image = layer.composite()
            if image:
                safe_layer_name = "".join([c if c.isalnum() or c in (' ', '_') else '_' for c in layer.name])
                png_name = f'{base_name}_image_{image_area_index}_{safe_layer_name}.png'
                png_path = os.path.join(psd_output_folder, png_name)
                image.save(png_path)
                left, top = layer.left, layer.top
                width, height = layer.width, layer.height

                # OCR文字识别
                ocr_texts = []
                if OCR_AVAILABLE and ocr_processor.ocr_reader:
                    print(f"    -> 正在进行PaddleOCR文字识别...")
                    print(f"    -> 图像信息: {image.size}, 模式: {image.mode}")

                    ocr_results = ocr_processor.extract_text_from_image(image, layer.name)

                    if ocr_results:
                        for result in ocr_results:
                            text = result['text']
                            confidence = result['confidence']
                            bbox = result['bbox']

                            ocr_x = left + bbox[0]
                            ocr_y = top + bbox[1]
                            ocr_w = bbox[2]
                            ocr_h = bbox[3]

                            estimated_font_size = max(12, min(48, int(ocr_h * 0.8)))

                            ini_content += f\"\"\"[template:0:cards:0:textAreas:{text_area_index}]
content = {text}
color = #000000
maxLength = {max(50, len(text) + 20)}
position:x = {ocr_x}
position:y = {ocr_y}
size:width = {ocr_w}
size:height = {ocr_h}
font=楷体
fontSize={estimated_font_size}
orientation=horizontal
source=PaddleOCR识别
confidence={confidence:.2f}

\"\"\"
                            text_area_index += 1
                            ocr_texts.append(text)
                    else:
                        print(f"    -> 未识别到有效文字内容")

                # 只有非背景图层才添加图像区域配置
                if layer != background_layer:
                    ini_content += f\"\"\"[template:0:cards:0:imageAreas:{image_area_index}]
url = {png_name}
position:x = {left}
position:y = {top}
size:width = {width}
size:height = {height}

\"\"\"
                    image_area_index += 1

                ocr_info = f" (PaddleOCR识别: {len(ocr_texts)}个文字)" if ocr_texts else ""
                layer_type = "背景图层" if layer == background_layer else "图片图层"
                print(f"    -> 已处理{layer_type}: {layer.name}{ocr_info}")
            else:
                print(f"    -> 跳过: 无法生成图像")
        except Exception as e:
            print(f"    -> 跳过: 处理图像时出错 - {str(e)}")

    # 保存INI文件
    ini_path = os.path.join(psd_output_folder, f"{base_name}.ini")
    with open(ini_path, 'w', encoding='utf-8') as f:
        f.write(ini_content)

    # 统计
    paddle_ocr_count = ini_content.count('source=PaddleOCR识别')
    total_text_areas = ini_content.count(':textAreas:')

    print(f"\\nINI配置文件已生成: {ini_path}")
    print(f"生成统计:")
    print(f"  - 图像区域: {image_area_index} 个")
    print(f"  - 文本区域: {total_text_areas} 个")
    print(f"    - PSD文本图层: {total_text_areas - paddle_ocr_count} 个")
    print(f"    - PaddleOCR识别文字: {paddle_ocr_count} 个")
    print(f"  - 背景图片: {'1 个' if background_png else '0 个'}")
    print(f"  - 缩略图: {'1 个' if thumbnail_url else '0 个'}")

if __name__ == '__main__':
    if len(sys.argv) == 3:
        psd_path = os.path.abspath(sys.argv[1])
        output_folder = os.path.abspath(sys.argv[2])
        
        print(f"处理路径: {psd_path}")
        print(f"输出路径: {output_folder}")
        
        if os.path.isfile(psd_path) and psd_path.lower().endswith('.psd'):
            process_psd(psd_path, output_folder)
            print(f"处理完成: {psd_path}")
        else:
            print(f"错误: 无效的PSD文件路径: {psd_path}")
    else:
        print("用法: python 123_simple_paddle.py <psd文件路径> <输出目录>")
'''
    
    with open("123_simple_paddle.py", "w", encoding="utf-8") as f:
        f.write(simple_ocr_code)
    
    print("   ✅ 简化版本已创建: 123_simple_paddle.py")

def main():
    """主函数"""
    print("🔧 修复PaddleOCR版本兼容性问题")
    print("=" * 50)
    
    # 1. 卸载当前版本
    uninstall_paddleocr()
    
    # 2. 安装稳定版本
    if not install_stable_versions():
        print("\n❌ 安装失败，建议手动安装:")
        print("   pip install paddlepaddle==2.4.2")
        print("   pip install paddleocr==*******")
        return False
    
    # 3. 测试安装
    if not test_installation():
        print("\n⚠️ 测试失败，创建简化版本...")
        create_simple_ocr_processor()
        print("\n💡 可以尝试使用简化版本:")
        print("   python 123_simple_paddle.py <psd文件> <输出目录>")
        return False
    
    # 4. 创建简化版本作为备用
    create_simple_ocr_processor()
    
    print("\n✅ PaddleOCR修复完成!")
    print("现在可以正常使用PaddleOCR功能了")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        
        if success:
            print(f"\n🚀 推荐使用:")
            print("   python psd_gui_tool.py  # GUI版本")
            print("   python 123_paddle.py <psd文件> <输出目录>  # 命令行版本")
            print("   python 123_simple_paddle.py <psd文件> <输出目录>  # 简化版本")
        else:
            print(f"\n🔧 如果仍有问题，可以:")
            print("   1. 使用简化版本: python 123_simple_paddle.py")
            print("   2. 回退到Tesseract: python 123_safe.py")
        
        input("\n按回车键退出...")
        
    except KeyboardInterrupt:
        print("\n\n❌ 修复被用户中断")
    except Exception as e:
        print(f"\n❌ 修复过程中出现错误: {e}")
        input("\n按回车键退出...")
