#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试图片重复问题修复
验证修复后是否还有重复的图片文件
"""

import os
import sys
import time
import requests
import json
from collections import Counter

def analyze_output_folder(folder_path):
    """分析输出文件夹，检查重复文件"""
    if not os.path.exists(folder_path):
        print(f"❌ 文件夹不存在: {folder_path}")
        return False
    
    print(f"📁 分析文件夹: {folder_path}")
    
    # 获取所有文件
    files = []
    for file in os.listdir(folder_path):
        if os.path.isfile(os.path.join(folder_path, file)):
            files.append(file)
    
    print(f"📄 总文件数: {len(files)}")
    
    # 分类文件
    background_files = [f for f in files if '_background_' in f]
    image_files = [f for f in files if '_image_' in f]
    thumbnail_files = [f for f in files if '_thumbnail' in f]
    ini_files = [f for f in files if f.endswith('.ini')]
    
    print(f"📊 文件分类:")
    print(f"  - 背景图: {len(background_files)} 个")
    print(f"  - 图像区域: {len(image_files)} 个")
    print(f"  - 缩略图: {len(thumbnail_files)} 个")
    print(f"  - INI配置: {len(ini_files)} 个")
    
    # 检查重复文件
    print(f"\n🔍 检查重复文件...")
    
    # 检查背景图是否在图像文件中重复
    duplicates_found = False
    
    for bg_file in background_files:
        # 提取背景图的图层名部分
        bg_layer_name = bg_file.replace('_background_', '_image_0_').replace('_background_', '_image_1_')
        
        # 检查是否有对应的图像文件
        matching_image_files = [f for f in image_files if bg_layer_name in f or f in bg_file]
        
        if matching_image_files:
            print(f"  ⚠️ 发现重复:")
            print(f"     背景图: {bg_file}")
            for img_file in matching_image_files:
                print(f"     图像文件: {img_file}")
            duplicates_found = True
    
    # 检查图像文件名是否有重复模式
    image_base_names = []
    for img_file in image_files:
        # 提取基础名称（去掉前缀）
        parts = img_file.split('_image_')
        if len(parts) > 1:
            base_name = '_'.join(parts[1].split('_')[1:])  # 去掉索引
            image_base_names.append(base_name)
    
    # 统计重复的基础名称
    name_counts = Counter(image_base_names)
    repeated_names = {name: count for name, count in name_counts.items() if count > 1}
    
    if repeated_names:
        print(f"  ⚠️ 发现重复的图层名:")
        for name, count in repeated_names.items():
            print(f"     '{name}': {count} 次")
            duplicates_found = True
    
    if not duplicates_found:
        print(f"  ✅ 未发现重复文件")
    
    return not duplicates_found

def analyze_ini_file(ini_path):
    """分析INI文件，检查配置是否正确"""
    if not os.path.exists(ini_path):
        print(f"❌ INI文件不存在: {ini_path}")
        return False
    
    print(f"\n📋 分析INI文件: {ini_path}")
    
    with open(ini_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 统计各种区域
    text_areas = content.count('[template:0:cards:0:textAreas:')
    image_areas = content.count('[template:0:cards:0:imageAreas:')
    
    print(f"📊 INI配置统计:")
    print(f"  - 文本区域: {text_areas} 个")
    print(f"  - 图像区域: {image_areas} 个")
    
    # 提取图像文件名
    image_urls = []
    lines = content.split('\n')
    for line in lines:
        if line.startswith('url = '):
            url = line.replace('url = ', '').strip()
            image_urls.append(url)
    
    print(f"  - 引用的图像文件: {len(image_urls)} 个")
    
    # 检查是否有重复的URL
    url_counts = Counter(image_urls)
    repeated_urls = {url: count for url, count in url_counts.items() if count > 1}
    
    if repeated_urls:
        print(f"  ⚠️ INI中发现重复的图像引用:")
        for url, count in repeated_urls.items():
            print(f"     '{url}': {count} 次")
        return False
    else:
        print(f"  ✅ INI配置无重复引用")
        return True

def test_web_processing():
    """测试Web处理，检查是否还有重复问题"""
    print("🧪 测试Web处理...")
    
    # 检查Web服务器
    try:
        response = requests.get('http://localhost:5000', timeout=5)
        print("  ✅ Web服务器正在运行")
    except requests.exceptions.RequestException:
        print("  ❌ Web服务器未运行，请先启动: python web_app.py")
        return False
    
    # 创建测试PSD文件
    test_psd_content = b"8BPS\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00"
    filename = "test_duplicate_fix.psd"
    
    try:
        # 上传文件
        files = {'file': (filename, test_psd_content, 'application/octet-stream')}
        response = requests.post('http://localhost:5000/upload', files=files, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            task_id = data['task_id']
            print(f"  ✅ 文件上传成功，任务ID: {task_id}")
            
            # 等待处理完成
            max_wait = 20
            start_time = time.time()
            
            while time.time() - start_time < max_wait:
                status_response = requests.get(f'http://localhost:5000/status/{task_id}', timeout=5)
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    
                    if status_data.get('status') == 'completed':
                        print(f"  ✅ 处理完成")
                        
                        # 分析结果
                        if 'result' in status_data:
                            result = status_data['result']
                            print(f"  📊 处理结果:")
                            print(f"     图像区域: {result.get('image_areas', 0)} 个")
                            print(f"     文本区域: {result.get('text_areas', 0)} 个")
                            print(f"     OCR识别: {result.get('ocr_count', 0)} 个")
                        
                        return True
                    elif status_data.get('status') == 'error':
                        print(f"  ❌ 处理失败: {status_data.get('message', '未知错误')}")
                        return False
                
                time.sleep(1)
            
            print(f"  ⏰ 处理超时")
            return False
            
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 PSD处理工具 - 图片重复问题修复验证")
    print("=" * 60)
    
    # 1. 分析现有的问题文件夹
    problem_folder = "11_处理结果_20250603_160025"
    if os.path.exists(problem_folder):
        print("📁 分析问题文件夹（修复前）:")
        analyze_output_folder(problem_folder)
        
        # 分析INI文件
        ini_file = os.path.join(problem_folder, "11.ini")
        if os.path.exists(ini_file):
            analyze_ini_file(ini_file)
    else:
        print(f"⚠️ 问题文件夹不存在: {problem_folder}")
    
    print("\n" + "=" * 60)
    
    # 2. 测试修复后的Web处理
    print("🧪 测试修复后的处理逻辑:")
    web_test_success = test_web_processing()
    
    # 3. 查找最新的输出文件夹进行分析
    print(f"\n📁 查找最新的输出文件夹...")
    
    output_folders = []
    for item in os.listdir('.'):
        if os.path.isdir(item) and '_处理结果_' in item:
            output_folders.append(item)
    
    if output_folders:
        # 按时间戳排序，获取最新的
        latest_folder = sorted(output_folders)[-1]
        print(f"📁 分析最新输出文件夹: {latest_folder}")
        
        folder_analysis_success = analyze_output_folder(latest_folder)
        
        # 分析对应的INI文件
        base_name = latest_folder.split('_处理结果_')[0]
        ini_file = os.path.join(latest_folder, f"{base_name}.ini")
        if os.path.exists(ini_file):
            ini_analysis_success = analyze_ini_file(ini_file)
        else:
            print(f"⚠️ 未找到INI文件: {ini_file}")
            ini_analysis_success = False
    else:
        print(f"⚠️ 未找到输出文件夹")
        folder_analysis_success = False
        ini_analysis_success = False
    
    # 4. 总结
    print("\n" + "=" * 60)
    print("📊 修复验证结果:")
    
    if web_test_success:
        print("  ✅ Web处理测试通过")
    else:
        print("  ❌ Web处理测试失败")
    
    if folder_analysis_success:
        print("  ✅ 文件夹分析通过（无重复文件）")
    else:
        print("  ❌ 文件夹分析失败（仍有重复文件）")
    
    if ini_analysis_success:
        print("  ✅ INI配置分析通过（无重复引用）")
    else:
        print("  ❌ INI配置分析失败（仍有重复引用）")
    
    all_success = web_test_success and folder_analysis_success and ini_analysis_success
    
    if all_success:
        print("\n🎉 修复验证成功！图片重复问题已解决")
        print("\n✨ 修复效果:")
        print("  - ✅ 背景图层不再重复保存为图像区域")
        print("  - ✅ 图像区域索引正确且连续")
        print("  - ✅ INI配置与实际文件一一对应")
        print("  - ✅ 文件命名规范统一")
    else:
        print("\n⚠️ 修复验证失败，仍需进一步调试")
    
    return all_success

if __name__ == "__main__":
    try:
        success = main()
        input(f"\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n⏹️ 验证被用户中断")
    except Exception as e:
        print(f"\n❌ 验证过程中出现错误: {e}")
        input("\n按回车键退出...")
