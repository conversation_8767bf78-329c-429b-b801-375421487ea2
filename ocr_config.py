#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
OCR配置文件
用于调整OCR识别参数
"""

# OCR识别配置
OCR_CONFIG = {
    # 置信度阈值 (0-100)
    # 数值越高，过滤越严格，识别结果越少但质量越高
    'confidence_threshold': 60,
    
    # 最小文本长度
    'min_text_length': 2,
    
    # 最小文本区域尺寸 (像素)
    'min_text_width': 10,
    'min_text_height': 10,
    
    # 是否启用文本验证
    'enable_text_validation': True,
    
    # 佛教相关关键词（这些词会被优先保留）
    'buddhist_keywords': [
        '普陀山', '观音', '菩萨', '佛', '禅', '寺', '庙', 
        '早安', '善念', '佛佑', '众生', '南无', '阿弥陀佛',
        '心经', '大悲咒', '莲花', '慈悲', '智慧', '功德'
    ],
    
    # 常见有意义词汇
    'meaningful_words': [
        '早安', '晚安', '您好', '谢谢', '平安', '健康', 
        '快乐', '幸福', '吉祥', '如意', '福气', '安康'
    ],
    
    # Tesseract配置参数
    'tesseract_configs': [
        # 第一个配置：限制字符集，专门针对佛教内容
        '--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz一二三四五六七八九十早安心存善念佛佑众生普陀山观音菩萨南无阿弥陀',
        # 第二个配置：统一文本块
        '--oem 3 --psm 6',
        # 第三个配置：单行文本
        '--oem 3 --psm 7'
    ]
}

# 调试配置
DEBUG_CONFIG = {
    # 是否保存调试图像
    'save_debug_images': True,
    
    # 是否显示详细日志
    'verbose_logging': True,
    
    # 是否显示所有识别结果（包括被过滤的）
    'show_filtered_results': False
}

def get_ocr_config():
    """获取OCR配置"""
    return OCR_CONFIG.copy()

def get_debug_config():
    """获取调试配置"""
    return DEBUG_CONFIG.copy()

def update_config(**kwargs):
    """更新配置"""
    for key, value in kwargs.items():
        if key in OCR_CONFIG:
            OCR_CONFIG[key] = value
            print(f"已更新配置: {key} = {value}")
        else:
            print(f"未知配置项: {key}")

def print_current_config():
    """打印当前配置"""
    print("当前OCR配置:")
    print("-" * 30)
    for key, value in OCR_CONFIG.items():
        if isinstance(value, list) and len(value) > 3:
            print(f"{key}: {len(value)} 个项目")
        else:
            print(f"{key}: {value}")

if __name__ == "__main__":
    print("OCR配置管理")
    print("=" * 40)
    
    print_current_config()
    
    print(f"\n调试配置:")
    print("-" * 30)
    for key, value in DEBUG_CONFIG.items():
        print(f"{key}: {value}")
    
    print(f"\n💡 使用说明:")
    print("1. 如果识别到太多无效文字，提高 confidence_threshold")
    print("2. 如果识别不到有效文字，降低 confidence_threshold")
    print("3. 可以添加更多关键词到 buddhist_keywords")
    print("4. 调整 min_text_length 过滤短文本")
    
    print(f"\n🔧 推荐设置:")
    print("- 严格模式: confidence_threshold = 70")
    print("- 平衡模式: confidence_threshold = 60 (当前)")
    print("- 宽松模式: confidence_threshold = 40")
