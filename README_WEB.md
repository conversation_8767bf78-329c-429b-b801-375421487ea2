# PSD文件处理工具 - Web版

🌐 基于Flask的在线PSD文件处理工具，支持通过浏览器访问，无需安装客户端软件。

## ✨ Web版特点

- **🌐 在线访问** - 通过浏览器直接使用，无需安装客户端
- **📱 响应式设计** - 支持PC、平板、手机等各种设备
- **⚡ 实时进度** - 实时显示处理进度和状态
- **📦 一键下载** - 处理完成后一键下载ZIP压缩包
- **🔄 后台处理** - 支持大文件后台异步处理
- **🧹 自动清理** - 自动清理过期文件，节省存储空间

## 🚀 快速开始

### 1. 安装依赖

```bash
python install_web_dependencies.py
```

### 2. 启动Web服务器

```bash
python web_app.py
```

### 3. 访问Web界面

在浏览器中打开：`http://localhost:5000`

## 📋 系统要求

- **Python**: 3.7 或更高版本
- **内存**: 建议 4GB 以上
- **存储**: 建议预留 2GB 临时存储空间
- **网络**: 支持局域网或公网访问

## 📦 依赖包

### Web框架
- `Flask` - Web应用框架
- `Werkzeug` - WSGI工具库

### 核心功能
- `psd-tools` - PSD文件解析
- `Pillow` - 图像处理
- `numpy` - 数值计算
- `opencv-python` - 计算机视觉
- `pytesseract` - OCR文字识别

## 🌐 Web界面功能

### 主要功能
1. **文件上传** - 支持拖拽上传和点击选择
2. **实时进度** - 显示处理进度条和状态信息
3. **结果统计** - 显示处理结果统计数据
4. **文件下载** - 一键下载处理结果ZIP包

### 支持的操作
- 📁 上传PSD文件（最大100MB）
- 🔄 实时查看处理进度
- 📊 查看处理结果统计
- 📥 下载完整结果包

## 🔧 部署方式

### 开发环境
```bash
# 启动开发服务器
python web_app.py

# 访问地址
http://localhost:5000
```

### 生产环境

#### 使用Gunicorn部署
```bash
# 安装Gunicorn
pip install gunicorn

# 启动生产服务器
gunicorn -w 4 -b 0.0.0.0:5000 web_app:app

# 后台运行
nohup gunicorn -w 4 -b 0.0.0.0:5000 web_app:app > web_app.log 2>&1 &
```

#### 使用Nginx反向代理
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        
        # 支持大文件上传
        client_max_body_size 100M;
    }
}
```

#### 使用Docker部署
```dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 5000

CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5000", "web_app:app"]
```

## 📁 项目结构

```
PSD处理工具Web版/
├── web_app.py                    # Web应用主文件
├── install_web_dependencies.py  # 依赖安装脚本
├── templates/
│   └── index.html               # Web界面模板
├── uploads/                     # 上传文件目录
├── outputs/                     # 输出文件目录
├── static/                      # 静态资源目录
└── README_WEB.md               # Web版说明文档
```

## 🔄 API接口

### 上传文件
```
POST /upload
Content-Type: multipart/form-data

参数:
- file: PSD文件

返回:
{
    "task_id": "uuid",
    "message": "文件上传成功，开始处理..."
}
```

### 查询状态
```
GET /status/<task_id>

返回:
{
    "status": "processing|completed|error",
    "progress": 50,
    "message": "正在处理图像图层...",
    "result": {...}  // 仅在completed状态时返回
}
```

### 下载结果
```
GET /download/<task_id>

返回: ZIP文件下载
```

### 清理过期文件
```
GET /cleanup

返回:
{
    "message": "清理了 X 个过期任务"
}
```

## ⚙️ 配置选项

### 文件大小限制
```python
app.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024  # 100MB
```

### 存储目录
```python
app.config['UPLOAD_FOLDER'] = 'uploads'    # 上传目录
app.config['OUTPUT_FOLDER'] = 'outputs'    # 输出目录
```

### 任务清理
- 自动清理1小时前的过期任务
- 可通过访问 `/cleanup` 手动清理

## 🛡️ 安全考虑

### 文件安全
- 只允许上传PSD文件
- 文件大小限制100MB
- 自动清理过期文件

### 访问控制
- 可配置IP白名单
- 支持基本认证
- 建议使用HTTPS

### 示例安全配置
```python
# 添加IP白名单
ALLOWED_IPS = ['127.0.0.1', '***********/24']

@app.before_request
def limit_remote_addr():
    if request.remote_addr not in ALLOWED_IPS:
        abort(403)
```

## 📊 性能优化

### 服务器配置
- 使用多进程部署（Gunicorn -w 4）
- 配置适当的内存限制
- 使用SSD存储提高IO性能

### 文件处理
- 大文件异步处理
- 自动压缩输出结果
- 定期清理临时文件

## 🔍 故障排除

### 常见问题

1. **上传失败**
   - 检查文件大小是否超过限制
   - 确认文件格式为PSD
   - 检查磁盘空间是否充足

2. **处理超时**
   - 检查服务器内存使用情况
   - 确认Tesseract OCR正常工作
   - 查看服务器日志

3. **下载失败**
   - 检查输出文件是否存在
   - 确认任务状态为completed
   - 检查文件权限

### 日志查看
```bash
# 查看应用日志
tail -f web_app.log

# 查看系统资源
htop
df -h
```

## 🔄 更新日志

### v1.0.0 (Web版)
- ✅ 完整的Web界面
- ✅ 文件上传和下载功能
- ✅ 实时进度显示
- ✅ 后台异步处理
- ✅ 自动文件清理
- ✅ 响应式设计
- ✅ RESTful API接口

## 📞 技术支持

### Web版特有问题
- 浏览器兼容性问题
- 网络连接问题
- 服务器部署问题

### 联系方式
如遇到问题，请提供：
1. 浏览器类型和版本
2. 错误信息截图
3. 服务器日志
4. PSD文件信息

---

**PSD文件处理工具Web版** - 让PSD处理更加便捷，随时随地在线使用！
