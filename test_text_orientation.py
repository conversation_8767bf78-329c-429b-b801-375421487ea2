#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试文本排版方向自动判断功能
"""

import os
import sys
import time
import requests
import json

def test_orientation_logic():
    """测试排版方向判断逻辑"""
    print("🧪 测试文本排版方向判断逻辑")
    print("=" * 60)
    
    # 模拟detect_text_orientation函数（更新版本）
    def detect_text_orientation(width, height, text_content=""):
        """模拟排版方向判断逻辑（改进版）"""
        aspect_ratio = width / height if height > 0 else 1

        # 检查是否包含中文
        has_chinese = any('\u4e00' <= c <= '\u9fff' for c in text_content) if text_content else False
        text_length = len(text_content.strip()) if text_content else 0

        # 改进的判断逻辑
        if text_content and has_chinese:
            # 中文文本的特殊判断逻辑

            # 检查是否包含标点符号
            has_punctuation = any(c in '，。、；：！？' for c in text_content)

            # 首先检查极端的宽高比情况（优先级最高）
            if aspect_ratio > 3.0:
                # 宽度明显大于高度，强制横排
                orientation = 'horizontal'
                reason = f"宽高比 {aspect_ratio:.2f} > 3.0，强制判断为横排"

            elif aspect_ratio < 0.4:
                # 高度明显大于宽度，强制竖排
                orientation = 'vertical'
                reason = f"宽高比 {aspect_ratio:.2f} < 0.4，强制判断为竖排"

            # 长文本且包含标点，但要考虑宽高比
            elif text_length >= 8 and has_punctuation and aspect_ratio <= 2.0:
                orientation = 'vertical'
                reason = f"中文长文本({text_length}字)且包含标点，宽高比适中，判断为竖排"

            # 检查文本密度
            elif text_length > 0:
                area = width * height
                char_density = text_length / area if area > 0 else 0

                # 如果字符密度较高且高度>宽度，倾向于竖排
                if char_density > 0.01 and height > width:
                    orientation = 'vertical'
                    reason = f"中文文本字符密度高({char_density:.4f})且高度>宽度，判断为竖排"

                # 如果是短文本（1-3字）且宽高比不是特别大
                elif text_length <= 3 and aspect_ratio < 2.5:
                    orientation = 'vertical'
                    reason = f"中文短文本({text_length}字)且宽高比适中，判断为竖排"

                # 宽度明显大于高度的情况
                elif aspect_ratio > 2.0:
                    orientation = 'horizontal'
                    reason = f"宽高比 {aspect_ratio:.2f} > 2.0，判断为横排"

                # 高度明显大于宽度的情况
                elif aspect_ratio < 0.6:
                    orientation = 'vertical'
                    reason = f"宽高比 {aspect_ratio:.2f} < 0.6，判断为竖排"

                # 默认情况：中文倾向于竖排
                else:
                    orientation = 'vertical'
                    reason = f"中文文本默认倾向于竖排"
            else:
                orientation = 'vertical' if height > width else 'horizontal'
                reason = f"无中文内容，根据宽高比判断"

        else:
            # 英文或无文本的判断逻辑
            if aspect_ratio > 2.0:
                orientation = 'horizontal'
                reason = f"宽高比 {aspect_ratio:.2f} > 2.0，判断为横排"
            elif aspect_ratio < 0.5:
                orientation = 'vertical'
                reason = f"宽高比 {aspect_ratio:.2f} < 0.5，判断为竖排"
            else:
                # 根据宽高比判断，而不是默认横排
                if height > width:
                    orientation = 'vertical'
                    reason = f"英文文本或无内容，高度>宽度，判断为竖排"
                else:
                    orientation = 'horizontal'
                    reason = f"英文文本或无内容，宽度≥高度，判断为横排"

        return orientation, reason
    
    # 测试用例（更新版本，包含实际场景）
    test_cases = [
        # (宽度, 高度, 文本内容, 预期结果, 描述)

        # 基于您图片中的实际场景
        (150, 200, "世界和阳光都刚刚醒来，温柔地包裹着你。", "vertical", "红框中的竖排长文本"),
        (100, 150, "早安", "vertical", "中文短文本，竖排"),
        (80, 120, "心存善念", "vertical", "中文中等文本，竖排"),

        # 明显的横排场景
        (400, 50, "世界和阳光都刚刚醒来，温柔地包裹着你。", "horizontal", "宽度明显大于高度的长文本"),
        (300, 80, "早安", "horizontal", "宽度明显大于高度的短文本"),

        # 明显的竖排场景
        (50, 300, "世界和阳光都刚刚醒来，温柔地包裹着你。", "vertical", "高度明显大于宽度的长文本"),
        (40, 200, "早安", "vertical", "高度明显大于宽度的短文本"),

        # 边界情况
        (120, 100, "早安", "vertical", "中文短文本，宽高比适中，倾向竖排"),
        (150, 120, "测试文本", "vertical", "中文中等文本，倾向竖排"),
        (200, 100, "Hello World", "horizontal", "英文文本，倾向横排"),

        # 包含标点的长文本
        (120, 180, "世界和阳光都刚刚醒来，温柔地包裹着你。", "vertical", "包含标点的中文长文本"),
        (100, 150, "心存善念，佛佑众生。", "vertical", "包含标点的中文文本"),

        # 无文本内容
        (200, 100, "", "horizontal", "无文本内容，宽度大于高度"),
        (100, 200, "", "vertical", "无文本内容，高度大于宽度"),
    ]
    
    print("📋 测试用例:")
    print("-" * 60)
    
    success_count = 0
    
    for i, (width, height, text, expected, description) in enumerate(test_cases):
        orientation, reason = detect_text_orientation(width, height, text)
        
        result_icon = "✅" if orientation == expected else "❌"
        print(f"{i+1:2d}. {result_icon} {description}")
        print(f"    尺寸: {width}x{height}, 文本: '{text}'")
        print(f"    判断: {orientation} ({reason})")
        print(f"    预期: {expected}")
        
        if orientation == expected:
            success_count += 1
        
        print()
    
    print(f"📊 测试结果: {success_count}/{len(test_cases)} 通过 ({success_count/len(test_cases)*100:.1f}%)")
    
    return success_count == len(test_cases)

def test_web_orientation():
    """测试Web版本的排版方向判断"""
    print("\n🌐 测试Web版本的排版方向判断")
    print("=" * 60)
    
    # 检查Web服务器
    try:
        response = requests.get('http://localhost:5000', timeout=5)
        print("✅ Web服务器正在运行")
    except requests.exceptions.RequestException:
        print("❌ Web服务器未运行，请先启动: python web_app.py")
        return False
    
    # 创建测试PSD文件
    test_psd_content = b"8BPS\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00"
    filename = "orientation_test.psd"
    
    try:
        # 上传文件
        files = {'file': (filename, test_psd_content, 'application/octet-stream')}
        response = requests.post('http://localhost:5000/upload', files=files, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            task_id = data['task_id']
            print(f"✅ 文件上传成功，任务ID: {task_id}")
            
            # 等待处理完成
            max_wait = 20
            start_time = time.time()
            
            while time.time() - start_time < max_wait:
                status_response = requests.get(f'http://localhost:5000/status/{task_id}', timeout=5)
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    
                    if status_data.get('status') == 'completed':
                        print(f"✅ 处理完成")
                        
                        # 分析结果
                        if 'result' in status_data:
                            result = status_data['result']
                            print(f"📊 处理结果:")
                            print(f"   文本区域: {result.get('text_areas', 0)} 个")
                            print(f"   OCR识别: {result.get('ocr_count', 0)} 个")
                            
                            # 下载并分析INI文件
                            return analyze_ini_orientation(task_id)
                        
                        return True
                    elif status_data.get('status') == 'error':
                        print(f"❌ 处理失败: {status_data.get('message', '未知错误')}")
                        return False
                
                time.sleep(1)
            
            print(f"⏰ 处理超时")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def analyze_ini_orientation(task_id):
    """分析INI文件中的排版方向"""
    print(f"\n📋 分析INI文件中的排版方向...")
    
    try:
        # 下载ZIP文件
        download_response = requests.get(f'http://localhost:5000/download/{task_id}', timeout=10)
        
        if download_response.status_code == 200:
            import tempfile
            import zipfile
            
            # 保存到临时文件
            with tempfile.NamedTemporaryFile(suffix='.zip', delete=False) as temp_file:
                temp_file.write(download_response.content)
                temp_zip_path = temp_file.name
            
            # 解压并分析INI文件
            with zipfile.ZipFile(temp_zip_path, 'r') as zipf:
                ini_files = [f for f in zipf.namelist() if f.endswith('.ini')]
                
                if ini_files:
                    ini_content = zipf.read(ini_files[0]).decode('utf-8')
                    
                    print(f"   📄 INI文件内容分析:")
                    
                    # 查找orientation设置
                    lines = ini_content.split('\n')
                    text_areas = []
                    current_area = {}
                    
                    for line in lines:
                        line = line.strip()
                        if line.startswith('[template:0:cards:0:textAreas:'):
                            if current_area:
                                text_areas.append(current_area)
                            current_area = {'index': line}
                        elif '=' in line and current_area:
                            key, value = line.split('=', 1)
                            current_area[key.strip()] = value.strip()
                    
                    if current_area:
                        text_areas.append(current_area)
                    
                    # 分析每个文本区域
                    orientation_found = False
                    for i, area in enumerate(text_areas):
                        if 'content' in area and 'orientation' in area:
                            content = area['content']
                            orientation = area['orientation']
                            width = area.get('size:width', '未知')
                            height = area.get('size:height', '未知')
                            source = area.get('source', 'PSD文本图层')
                            
                            print(f"     {i+1}. 文本: '{content}'")
                            print(f"        尺寸: {width}x{height}")
                            print(f"        排版: {orientation}")
                            print(f"        来源: {source}")
                            print()
                            
                            orientation_found = True
                    
                    if orientation_found:
                        print(f"   ✅ 成功找到orientation设置，不再是写死的值")
                        result = True
                    else:
                        print(f"   ❌ 未找到orientation设置")
                        result = False
                else:
                    print(f"   ❌ ZIP文件中未找到INI文件")
                    result = False
            
            # 清理临时文件
            os.unlink(temp_zip_path)
            return result
            
        else:
            print(f"   ❌ 下载ZIP文件失败: HTTP {download_response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 分析INI文件出错: {e}")
        return False

def main():
    """主函数"""
    print("🧪 PSD处理工具 - 文本排版方向自动判断测试")
    print("=" * 70)
    
    # 1. 测试判断逻辑
    logic_test_success = test_orientation_logic()
    
    # 2. 测试Web版本
    web_test_success = test_web_orientation()
    
    # 3. 清理测试文件
    print(f"\n🧹 清理测试文件...")
    try:
        cleanup_response = requests.get('http://localhost:5000/cleanup', timeout=10)
        if cleanup_response.status_code == 200:
            cleanup_data = cleanup_response.json()
            print(f"  ✅ {cleanup_data.get('message', '清理完成')}")
        else:
            print(f"  ⚠️ 清理请求失败")
    except:
        print(f"  ⚠️ 清理请求出错")
    
    # 4. 总结
    print("\n" + "=" * 70)
    print("📊 文本排版方向判断测试结果:")
    print(f"  判断逻辑测试: {'✅ 通过' if logic_test_success else '❌ 失败'}")
    print(f"  Web版本测试: {'✅ 通过' if web_test_success else '❌ 失败'}")
    
    all_success = logic_test_success and web_test_success
    
    if all_success:
        print("\n🎉 所有测试通过！文本排版方向自动判断功能正常工作")
        print("\n✨ 判断规则:")
        print("  - 宽高比 > 1.5  → horizontal (横排)")
        print("  - 宽高比 < 0.67 → vertical (竖排)")
        print("  - 宽高比 ≈ 1.0  → 根据文本长度和宽高比微调")
        print("    * 短文本(≤2字) → 倾向于 vertical")
        print("    * 长文本(≥6字) → 倾向于 horizontal")
        print("    * 中等文本     → 根据宽高比判断")
    else:
        print("\n⚠️ 部分测试失败，文本排版方向判断功能需要改进")
    
    return all_success

if __name__ == "__main__":
    try:
        success = main()
        input(f"\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        input("\n按回车键退出...")
