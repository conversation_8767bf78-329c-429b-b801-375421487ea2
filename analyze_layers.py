#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from psd_tools import PSDImage
import os

def analyze_psd_layers(psd_path):
    """分析PSD文件的图层结构"""
    print(f"\n正在分析PSD文件: {psd_path}")
    print("=" * 60)
    
    try:
        psd = PSDImage.open(psd_path)
        print(f"PSD文件信息:")
        print(f"  - 尺寸: {psd.width} x {psd.height} 像素")
        print(f"  - 颜色模式: {psd.color_mode}")
        print(f"  - 总图层数: {len(list(psd.descendants()))}")
        print("\n图层结构分析:")
        print("-" * 40)
        
        layer_count = 0
        text_layer_count = 0
        image_layer_count = 0
        group_count = 0
        
        def analyze_layer(layer, depth=0):
            nonlocal layer_count, text_layer_count, image_layer_count, group_count
            
            indent = "  " * depth
            layer_count += 1
            
            # 基本信息
            layer_type = "未知"
            layer_info = ""
            
            if layer.is_group():
                layer_type = "图层组"
                group_count += 1
                layer_info = f"(包含 {len(layer)} 个子图层)"
            elif layer.kind == 'type':
                layer_type = "文字图层"
                text_layer_count += 1
                try:
                    text_content = layer.text[:20] + "..." if len(layer.text) > 20 else layer.text
                    layer_info = f"文本: '{text_content}'"
                except:
                    layer_info = "文本: [无法读取]"
            else:
                layer_type = "图像图层"
                image_layer_count += 1
                layer_info = f"类型: {layer.kind}"
            
            # 显示状态
            visibility = "可见" if layer.is_visible() else "隐藏"
            
            print(f"{indent}├─ 图层 {layer_count}: {layer.name}")
            print(f"{indent}   ├─ 类型: {layer_type}")
            print(f"{indent}   ├─ 状态: {visibility}")
            print(f"{indent}   ├─ 位置: ({layer.left}, {layer.top})")
            print(f"{indent}   ├─ 尺寸: {layer.width} x {layer.height}")
            if layer_info:
                print(f"{indent}   ├─ 详情: {layer_info}")
            
            # 如果是文字图层，显示更多详细信息
            if layer.kind == 'type':
                try:
                    # 尝试获取字体信息
                    font_info = "未知字体"
                    font_size = "未知大小"
                    
                    if hasattr(layer, 'engine_data') and layer.engine_data:
                        engine_data = layer.engine_data
                        if 'DocumentResources' in engine_data and 'FontSet' in engine_data['DocumentResources']:
                            font_set = engine_data['DocumentResources']['FontSet']
                            if font_set and len(font_set) > 0:
                                first_font = font_set[0]
                                if 'Name' in first_font:
                                    font_info = first_font['Name']
                                if 'Size' in first_font:
                                    font_size = f"{first_font['Size']}pt"
                    
                    print(f"{indent}   ├─ 字体: {font_info}")
                    print(f"{indent}   └─ 字号: {font_size}")
                except Exception as e:
                    print(f"{indent}   └─ 字体信息: 无法获取")
            else:
                print(f"{indent}   └─ ")
            
            # 递归处理子图层 - 修复递归逻辑
            if layer.is_group():
                for sublayer in layer:
                    analyze_layer(sublayer, depth + 1)
        
        # 分析所有顶级图层
        for layer in psd:
            analyze_layer(layer)
        
        # 统计信息
        print("\n" + "=" * 60)
        print("图层统计信息:")
        print(f"  - 总图层数: {layer_count}")
        print(f"  - 文字图层: {text_layer_count}")
        print(f"  - 图像图层: {image_layer_count}")
        print(f"  - 图层组: {group_count}")
        
        # 可见图层统计
        visible_layers = [layer for layer in psd.descendants() if layer.is_visible() and not layer.is_group()]
        print(f"  - 可见图层: {len(visible_layers)}")
        
        return True
        
    except Exception as e:
        print(f"分析PSD文件时出错: {e}")
        return False

def main():
    psd_folder = 'psd_files'
    
    if not os.path.exists(psd_folder):
        print(f"错误: 找不到文件夹 '{psd_folder}'")
        return
    
    psd_files = [f for f in os.listdir(psd_folder) if f.lower().endswith('.psd')]
    
    if not psd_files:
        print(f"错误: 在 '{psd_folder}' 文件夹中没有找到PSD文件")
        return
    
    print("PSD文件图层结构分析工具")
    print("=" * 60)
    
    for psd_file in psd_files:
        psd_path = os.path.join(psd_folder, psd_file)
        analyze_psd_layers(psd_path)
        print("\n")

if __name__ == "__main__":
    main()