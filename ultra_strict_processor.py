#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
超严格OCR处理器 - 专门解决无效识别问题
"""

import os
import sys
from psd_tools import PSDImage
import numpy as np
from PIL import Image
import cv2

# 设置控制台编码
if sys.platform.startswith('win'):
    import io
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

# 检查Tesseract
try:
    import pytesseract
    OCR_AVAILABLE = True
    print("✅ 使用 Tesseract OCR 引擎（超严格模式）")
except ImportError:
    OCR_AVAILABLE = False
    print("❌ Tesseract 不可用")

class UltraStrictOCRProcessor:
    """超严格OCR处理器"""
    
    def __init__(self):
        self.ocr_available = OCR_AVAILABLE
    
    def extract_text_from_image(self, image, layer_name=""):
        """从图像中提取文字（超严格过滤）"""
        if not self.ocr_available:
            return []
        
        try:
            # 图像预处理
            if isinstance(image, Image.Image):
                img_array = np.array(image)
            else:
                img_array = image
            
            # 转换RGBA到RGB
            if len(img_array.shape) == 3 and img_array.shape[2] == 4:
                rgb_array = np.ones((img_array.shape[0], img_array.shape[1], 3), dtype=np.uint8) * 255
                alpha = img_array[:, :, 3:4] / 255.0
                rgb_array = rgb_array * (1 - alpha) + img_array[:, :, :3] * alpha
                img_array = rgb_array.astype(np.uint8)
            
            # 转换为灰度
            if len(img_array.shape) == 3:
                gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            else:
                gray = img_array
            
            # 增强图像预处理
            # 1. 高斯模糊去噪
            blurred = cv2.GaussianBlur(gray, (3, 3), 0)
            
            # 2. 自适应阈值二值化
            binary = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
            
            # 3. 形态学操作
            kernel = np.ones((2, 2), np.uint8)
            cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
            
            text_results = []
            
            # 使用多种严格配置
            configs = [
                # 专门针对中文的配置
                '--oem 3 --psm 6 -c tessedit_char_whitelist=一二三四五六七八九十早安心存善念佛佑众生普陀山观音菩萨南无阿弥陀',
                # 标准配置
                '--oem 3 --psm 8',
                '--oem 3 --psm 7'
            ]
            
            all_results = []
            
            for config in configs:
                try:
                    data = pytesseract.image_to_data(cleaned, config=config, 
                                                   output_type=pytesseract.Output.DICT, 
                                                   lang='chi_sim+eng')
                    
                    for i in range(len(data['text'])):
                        text = data['text'][i].strip()
                        conf = int(data['conf'][i]) if data['conf'][i] != -1 else 0
                        
                        # 超严格过滤条件
                        if (conf > 70 and  # 提高置信度阈值到70%
                            len(text) >= 2 and  # 至少2个字符
                            self._is_ultra_valid_text(text)):  # 超严格文本验证
                            
                            x, y, w, h = data['left'][i], data['top'][i], data['width'][i], data['height'][i]
                            
                            # 检查区域大小
                            if w >= 15 and h >= 15:  # 提高最小尺寸要求
                                all_results.append({
                                    'text': text,
                                    'confidence': conf / 100.0,
                                    'bbox': (x, y, w, h),
                                    'source': f'Tesseract识别-{layer_name}',
                                    'config': config
                                })
                except Exception as e:
                    continue
            
            # 去重并选择最佳结果
            seen_texts = set()
            for result in sorted(all_results, key=lambda x: x['confidence'], reverse=True):
                text = result['text']
                if text not in seen_texts:
                    text_results.append(result)
                    seen_texts.add(text)
                    print(f"      - '{text}' (置信度: {result['confidence']:.3f})")
            
            if not text_results:
                print(f"    -> ❌ 未识别到符合超严格标准的文字")
            
            return text_results
            
        except Exception as e:
            print(f"    OCR识别出错: {str(e)}")
            return []
    
    def _is_ultra_valid_text(self, text):
        """超严格文本验证"""
        if not text or len(text.strip()) < 2:
            return False
        
        # 过滤纯英文字母（除非是有意义的词汇）
        if text.isalpha() and len(text) <= 3:
            # 只允许特定的短英文词汇
            allowed_short_words = ['OCR', 'PDF', 'PNG', 'JPG']
            if text.upper() not in allowed_short_words:
                return False
        
        # 过滤纯符号
        meaningless_chars = set('.,;:!?@#$%^&*()[]{}|\\/<>~`"\'')
        if all(c in meaningless_chars or c.isspace() for c in text):
            return False
        
        # 过滤纯数字（除非是年份等有意义数字）
        if text.isdigit():
            num = int(text)
            # 只允许可能的年份
            if not (1900 <= num <= 2100):
                return False
        
        # 过滤常见的OCR误识别
        invalid_patterns = [
            'AW', 'De', 'ee', 'wae', 'fea', 'eae', 'an', 'LY', 'ied',
            'at', 'to', 'in', 'on', 'is', 'it', 'be', 'or', 'as',
            'II', 'III', 'IV', 'VI', 'VII', 'VIII', 'IX', 'XI'
        ]
        if text in invalid_patterns:
            return False
        
        # 检查是否包含中文
        has_chinese = any('\u4e00' <= c <= '\u9fff' for c in text)
        
        # 佛教相关关键词（绝对优先保留）
        buddhist_keywords = [
            '普陀山', '观音', '菩萨', '佛', '禅', '寺', '庙', 
            '早安', '善念', '佛佑', '众生', '南无', '阿弥陀佛',
            '心经', '大悲咒', '莲花', '慈悲', '智慧', '功德',
            '观世音', '大慈大悲', '救苦救难'
        ]
        
        # 如果包含佛教关键词，直接通过
        if any(keyword in text for keyword in buddhist_keywords):
            return True
        
        # 常见有意义的中文词汇
        meaningful_chinese = [
            '早安', '晚安', '您好', '谢谢', '平安', '健康', 
            '快乐', '幸福', '吉祥', '如意', '福气', '安康',
            '祝福', '恭喜', '新年', '春节', '中秋', '国庆'
        ]
        
        if any(word in text for word in meaningful_chinese):
            return True
        
        # 如果包含中文且长度合适，通过
        if has_chinese and len(text) >= 2:
            return True
        
        # 有意义的英文词汇（长度>=4）
        if not has_chinese and len(text) >= 4:
            # 检查是否是常见英文单词
            common_words = [
                'hello', 'world', 'good', 'morning', 'evening', 'night',
                'thank', 'please', 'welcome', 'happy', 'love', 'peace'
            ]
            if text.lower() in common_words:
                return True
        
        return False

def process_psd(psd_path, base_output_folder):
    base_name = os.path.splitext(os.path.basename(psd_path))[0]
    psd_output_folder = os.path.join(base_output_folder, base_name)
    if not os.path.exists(psd_output_folder):
        os.makedirs(psd_output_folder)

    # 初始化超严格OCR处理器
    ocr_processor = UltraStrictOCRProcessor()
    print(f"OCR功能状态: {'超严格Tesseract模式' if OCR_AVAILABLE else '不可用'}")

    psd = PSDImage.open(psd_path)
    all_layers = [layer for layer in psd.descendants() if layer.is_visible() and not layer.is_group()]

    # 找背景图层
    background_layer = None
    for layer in all_layers:
        if '背景' in layer.name or 'background' in layer.name.lower():
            background_layer = layer
            break
    
    if not background_layer:
        image_layers = [layer for layer in all_layers if layer.kind != 'type']
        if image_layers:
            background_layer = max(image_layers, key=lambda l: l.width * l.height)

    if background_layer:
        print(f"找到背景图层: '{background_layer.name}' ({background_layer.width}x{background_layer.height})")

    # 生成背景图和缩略图
    background_png = ''
    if background_layer:
        safe_layer_name = "".join([c if c.isalnum() or c in (' ', '_') else '_' for c in background_layer.name])
        background_png = f'{base_name}_background_{safe_layer_name}.png'
        background_path = os.path.join(psd_output_folder, background_png)
        image = background_layer.composite()
        if image:
            image.save(background_path)

    thumbnail_name = f'{base_name}_thumbnail.png'
    thumbnail_path = os.path.join(psd_output_folder, thumbnail_name)
    try:
        thumbnail = psd.composite()
        if thumbnail:
            thumbnail.thumbnail((300, 200))
            thumbnail.save(thumbnail_path)
            thumbnail_url = thumbnail_name
        else:
            thumbnail_url = ''
    except:
        thumbnail_url = ''

    # 生成INI内容
    ini_content = f"""[template:0]
name = {base_name}模板
thumbnail = {thumbnail_url}

[template:0:cards:0]
background = {background_png}

"""

    text_area_index = 0
    image_area_index = 0

    print(f"\n处理图层:")

    # 处理文本图层
    text_layers = [layer for layer in all_layers if layer.kind == 'type' and layer.is_visible()]
    for layer in text_layers:
        print(f"  处理文本图层: '{layer.name}'")
        text = layer.text
        left, top = layer.left, layer.top
        width, height = layer.width, layer.height

        ini_content += f"""[template:0:cards:0:textAreas:{text_area_index}]
content = {text}
color = #000000
maxLength = {max(50, len(text) + 20)}
position:x = {left}
position:y = {top}
size:width = {width}
size:height = {height}
font=楷体
fontSize=20
orientation=horizontal

"""
        text_area_index += 1
        print(f"    -> 已添加文字区域: '{text}'")

    # 处理图像图层
    image_layers = [layer for layer in all_layers if layer.kind != 'type' and not layer.is_group() and layer.is_visible()]
    for layer in image_layers:
        print(f"  处理图像图层: '{layer.name}' (尺寸: {layer.width}x{layer.height})")

        if layer.width <= 10 or layer.height <= 10:
            print(f"    -> 跳过: 图层尺寸太小")
            continue

        try:
            image = layer.composite()
            if image:
                safe_layer_name = "".join([c if c.isalnum() or c in (' ', '_') else '_' for c in layer.name])
                png_name = f'{base_name}_image_{image_area_index}_{safe_layer_name}.png'
                png_path = os.path.join(psd_output_folder, png_name)
                image.save(png_path)
                left, top = layer.left, layer.top
                width, height = layer.width, layer.height

                # 超严格OCR文字识别
                ocr_texts = []
                if OCR_AVAILABLE:
                    print(f"    -> 正在进行超严格OCR识别...")
                    print(f"    -> 图像信息: {image.size}, 模式: {image.mode}")

                    # 保存调试图像
                    debug_image_path = os.path.join(psd_output_folder, f"debug_ultra_{safe_layer_name}.png")
                    image.save(debug_image_path)
                    print(f"    -> 调试图像已保存: {debug_image_path}")

                    ocr_results = ocr_processor.extract_text_from_image(image, layer.name)

                    if ocr_results:
                        print(f"    -> ✅ 识别成功! 找到 {len(ocr_results)} 个高质量文字:")
                        for result in ocr_results:
                            text = result['text']
                            confidence = result['confidence']
                            bbox = result['bbox']

                            ocr_x = left + bbox[0]
                            ocr_y = top + bbox[1]
                            ocr_w = bbox[2]
                            ocr_h = bbox[3]

                            estimated_font_size = max(12, min(48, int(ocr_h * 0.8)))

                            ini_content += f"""[template:0:cards:0:textAreas:{text_area_index}]
content = {text}
color = #000000
maxLength = {max(50, len(text) + 20)}
position:x = {ocr_x}
position:y = {ocr_y}
size:width = {ocr_w}
size:height = {ocr_h}
font=楷体
fontSize={estimated_font_size}
orientation=horizontal
source=超严格OCR识别
confidence={confidence:.3f}

"""
                            text_area_index += 1
                            ocr_texts.append(text)

                # 只有非背景图层才添加图像区域配置
                if layer != background_layer:
                    ini_content += f"""[template:0:cards:0:imageAreas:{image_area_index}]
url = {png_name}
position:x = {left}
position:y = {top}
size:width = {width}
size:height = {height}

"""
                    image_area_index += 1

                ocr_info = f" (超严格OCR识别: {len(ocr_texts)}个文字)" if ocr_texts else ""
                layer_type = "背景图层" if layer == background_layer else "图片图层"
                print(f"    -> 已处理{layer_type}: {layer.name}{ocr_info}")
            else:
                print(f"    -> 跳过: 无法生成图像")
        except Exception as e:
            print(f"    -> 跳过: 处理图像时出错 - {str(e)}")

    # 保存INI文件
    ini_path = os.path.join(psd_output_folder, f"{base_name}.ini")
    with open(ini_path, 'w', encoding='utf-8') as f:
        f.write(ini_content)

    # 统计
    ocr_count = ini_content.count('source=超严格OCR识别')
    total_text_areas = ini_content.count(':textAreas:')

    print(f"\nINI配置文件已生成: {ini_path}")
    print(f"生成统计:")
    print(f"  - 图像区域: {image_area_index} 个")
    print(f"  - 文本区域: {total_text_areas} 个")
    print(f"    - PSD文本图层: {total_text_areas - ocr_count} 个")
    print(f"    - 超严格OCR识别文字: {ocr_count} 个")
    print(f"  - 背景图片: {'1 个' if background_png else '0 个'}")
    print(f"  - 缩略图: {'1 个' if thumbnail_url else '0 个'}")

if __name__ == '__main__':
    if len(sys.argv) == 3:
        psd_path = os.path.abspath(sys.argv[1])
        output_folder = os.path.abspath(sys.argv[2])
        
        print(f"处理路径: {psd_path}")
        print(f"输出路径: {output_folder}")
        
        if os.path.isfile(psd_path) and psd_path.lower().endswith('.psd'):
            process_psd(psd_path, output_folder)
            print(f"处理完成: {psd_path}")
        else:
            print(f"错误: 无效的PSD文件路径: {psd_path}")
    else:
        print("用法: python ultra_strict_processor.py <psd文件路径> <输出目录>")
