#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试连续上传相同PSD文件的重复处理
验证单文件和批量处理中的去重机制
"""

import os
import sys
import time
import requests
import json
import zipfile
import tempfile

def test_single_duplicate_uploads():
    """测试连续单文件上传相同文件"""
    print("📄 测试连续单文件上传相同文件...")
    
    # 创建测试PSD文件
    test_psd_content = b"8BPS\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00"
    filename = "duplicate_test.psd"
    
    upload_results = []
    
    # 连续上传3次相同文件
    for i in range(3):
        print(f"  第{i+1}次上传...")
        
        try:
            files = {'file': (filename, test_psd_content, 'application/octet-stream')}
            response = requests.post('http://localhost:5000/upload', files=files, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                task_id = data['task_id']
                print(f"    ✅ 上传成功，任务ID: {task_id}")
                
                # 等待处理完成
                max_wait = 15
                start_time = time.time()
                
                while time.time() - start_time < max_wait:
                    status_response = requests.get(f'http://localhost:5000/status/{task_id}', timeout=5)
                    if status_response.status_code == 200:
                        status_data = status_response.json()
                        
                        if status_data.get('status') == 'completed':
                            result = status_data.get('result', {})
                            zip_filename = result.get('zip_display_name', '')
                            upload_results.append(zip_filename)
                            print(f"    ✅ 处理完成: {zip_filename}")
                            break
                        elif status_data.get('status') == 'error':
                            print(f"    ❌ 处理失败: {status_data.get('message', '未知错误')}")
                            break
                    
                    time.sleep(1)
                
            else:
                print(f"    ❌ 上传失败: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"    ❌ 上传出错: {e}")
    
    # 检查文件名是否重复
    print(f"\n  📊 单文件上传结果分析:")
    print(f"    生成的文件名:")
    for i, filename in enumerate(upload_results):
        print(f"      {i+1}. {filename}")
    
    # 检查重复
    unique_files = set(upload_results)
    if len(unique_files) == len(upload_results):
        print(f"    ✅ 无重复文件名，时间戳机制工作正常")
        return True
    else:
        print(f"    ❌ 发现重复文件名: {len(upload_results) - len(unique_files)} 个")
        return False

def test_batch_duplicate_uploads():
    """测试批量上传包含重复文件"""
    print("\n📚 测试批量上传包含重复文件...")
    
    # 创建测试PSD文件
    test_psd_content = b"8BPS\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00"
    
    # 准备重复文件列表
    test_files = [
        ("same_file.psd", test_psd_content),
        ("same_file.psd", test_psd_content),  # 重复文件
        ("different_file.psd", test_psd_content),
        ("same_file.psd", test_psd_content),  # 再次重复
    ]
    
    try:
        # 准备多文件上传
        files = []
        for filename, content in test_files:
            files.append(('files', (filename, content, 'application/octet-stream')))
        
        print(f"  📤 上传 {len(test_files)} 个文件（包含重复）...")
        response = requests.post('http://localhost:5000/upload', files=files, timeout=15)
        
        if response.status_code == 200:
            data = response.json()
            task_id = data['task_id']
            print(f"  ✅ 批量上传成功，任务ID: {task_id}")
            
            # 等待批量处理完成
            max_wait = 30
            start_time = time.time()
            
            while time.time() - start_time < max_wait:
                status_response = requests.get(f'http://localhost:5000/status/{task_id}', timeout=5)
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    progress = status_data.get('progress', 0)
                    message = status_data.get('message', '处理中...')
                    status = status_data.get('status', 'unknown')
                    
                    print(f"    进度: {progress}% - {message}")
                    
                    if status == 'completed':
                        print(f"  ✅ 批量处理完成!")
                        
                        # 下载并分析批量ZIP文件
                        return analyze_batch_zip(task_id)
                    elif status == 'error':
                        print(f"  ❌ 批量处理失败: {message}")
                        return False
                
                time.sleep(2)
            
            print(f"  ⏰ 批量处理超时")
            return False
            
    except Exception as e:
        print(f"  ❌ 批量上传出错: {e}")
        return False

def analyze_batch_zip(task_id):
    """分析批量ZIP文件内容"""
    print(f"\n  🔍 分析批量ZIP文件内容...")
    
    try:
        # 下载ZIP文件
        download_response = requests.get(f'http://localhost:5000/download/{task_id}', timeout=10)
        
        if download_response.status_code == 200:
            # 保存到临时文件
            with tempfile.NamedTemporaryFile(suffix='.zip', delete=False) as temp_file:
                temp_file.write(download_response.content)
                temp_zip_path = temp_file.name
            
            # 分析ZIP内容
            with zipfile.ZipFile(temp_zip_path, 'r') as zipf:
                file_list = zipf.namelist()
                
                print(f"    📦 ZIP文件包含 {len(file_list)} 个文件:")
                for i, filename in enumerate(file_list):
                    print(f"      {i+1}. {filename}")
                
                # 检查重复
                unique_files = set(file_list)
                if len(unique_files) == len(file_list):
                    print(f"    ✅ 批量ZIP无重复文件，去重机制工作正常")
                    result = True
                else:
                    print(f"    ❌ 批量ZIP发现重复文件: {len(file_list) - len(unique_files)} 个")
                    result = False
            
            # 清理临时文件
            os.unlink(temp_zip_path)
            return result
            
        else:
            print(f"    ❌ 下载ZIP文件失败: HTTP {download_response.status_code}")
            return False
            
    except Exception as e:
        print(f"    ❌ 分析ZIP文件出错: {e}")
        return False

def test_timestamp_precision():
    """测试时间戳精度"""
    print("\n🕐 测试时间戳精度...")
    
    # 快速连续上传，测试毫秒级时间戳
    test_psd_content = b"8BPS\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00"
    filename = "timestamp_test.psd"
    
    timestamps = []
    
    for i in range(5):
        try:
            files = {'file': (filename, test_psd_content, 'application/octet-stream')}
            response = requests.post('http://localhost:5000/upload', files=files, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                task_id = data['task_id']
                
                # 快速检查任务创建时间
                status_response = requests.get(f'http://localhost:5000/status/{task_id}', timeout=2)
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    created_at = status_data.get('created_at', '')
                    timestamps.append(created_at)
                    print(f"    第{i+1}次: 任务创建时间 {created_at}")
            
            # 短暂延迟
            time.sleep(0.1)
            
        except Exception as e:
            print(f"    第{i+1}次上传失败: {e}")
    
    # 检查时间戳是否都不同
    unique_timestamps = set(timestamps)
    if len(unique_timestamps) == len(timestamps):
        print(f"    ✅ 时间戳精度足够，{len(timestamps)} 个时间戳都不同")
        return True
    else:
        print(f"    ⚠️ 发现重复时间戳: {len(timestamps) - len(unique_timestamps)} 个")
        return False

def main():
    """主函数"""
    print("🧪 PSD处理工具 - 重复文件处理测试")
    print("=" * 60)
    
    # 检查Web服务器
    try:
        response = requests.get('http://localhost:5000', timeout=5)
        print("✅ Web服务器正在运行")
    except requests.exceptions.RequestException:
        print("❌ Web服务器未运行，请先启动: python web_app.py")
        return False
    
    # 1. 测试单文件重复上传
    single_test_success = test_single_duplicate_uploads()
    
    # 2. 测试批量重复上传
    batch_test_success = test_batch_duplicate_uploads()
    
    # 3. 测试时间戳精度
    timestamp_test_success = test_timestamp_precision()
    
    # 4. 清理测试文件
    print(f"\n🧹 清理测试文件...")
    try:
        cleanup_response = requests.get('http://localhost:5000/cleanup', timeout=10)
        if cleanup_response.status_code == 200:
            cleanup_data = cleanup_response.json()
            print(f"  ✅ {cleanup_data.get('message', '清理完成')}")
        else:
            print(f"  ⚠️ 清理请求失败")
    except:
        print(f"  ⚠️ 清理请求出错")
    
    # 5. 总结
    print("\n" + "=" * 60)
    print("📊 重复文件处理测试结果:")
    print(f"  单文件重复上传: {'✅ 通过' if single_test_success else '❌ 失败'}")
    print(f"  批量重复上传: {'✅ 通过' if batch_test_success else '❌ 失败'}")
    print(f"  时间戳精度: {'✅ 通过' if timestamp_test_success else '❌ 失败'}")
    
    all_success = single_test_success and batch_test_success and timestamp_test_success
    
    if all_success:
        print("\n🎉 所有测试通过！重复文件处理机制工作正常")
        print("\n✨ 去重机制:")
        print("  - ✅ 单文件：毫秒级时间戳 + 序号避重")
        print("  - ✅ 批量：智能重命名避免ZIP内重复")
        print("  - ✅ 图层：背景图层去重，索引连续")
        print("  - ✅ 文件：高精度时间戳防冲突")
    else:
        print("\n⚠️ 部分测试失败，重复处理机制需要改进")
    
    return all_success

if __name__ == "__main__":
    try:
        success = main()
        input(f"\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        input("\n按回车键退出...")
