#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
终极PSD处理方案：智能选择最佳OCR引擎
"""

import os
import sys
from psd_tools import PSDImage
import numpy as np
from PIL import Image

# 设置控制台编码
if sys.platform.startswith('win'):
    import io
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

# 智能OCR引擎选择
OCR_ENGINE = None
OCR_TYPE = "无"

# 尝试PaddleOCR
try:
    from paddleocr import PaddleOCR
    # 测试初始化
    test_ocr = PaddleOCR(lang='ch')
    OCR_ENGINE = "paddle"
    OCR_TYPE = "PaddleOCR"
    print("✅ 使用 PaddleOCR 引擎")
    del test_ocr
except Exception as e:
    print(f"⚠️ PaddleOCR 不可用: {e}")
    
    # 备选：尝试Tesseract
    try:
        import pytesseract
        OCR_ENGINE = "tesseract"
        OCR_TYPE = "Tesseract"
        print("✅ 使用 Tesseract OCR 引擎")
    except ImportError:
        print("❌ 未找到可用的OCR引擎")

class UltimateOCRProcessor:
    """终极OCR处理器"""
    
    def __init__(self):
        self.ocr_reader = None
        self._init_ocr()
    
    def _init_ocr(self):
        """初始化OCR引擎"""
        if OCR_ENGINE == "paddle":
            try:
                from paddleocr import PaddleOCR
                self.ocr_reader = PaddleOCR(lang='ch')
                print("    PaddleOCR 初始化成功")
            except Exception as e:
                print(f"    PaddleOCR 初始化失败: {e}")
                self.ocr_reader = None
        
        elif OCR_ENGINE == "tesseract":
            try:
                import pytesseract
                self.ocr_reader = "tesseract"  # 标记可用
                print("    Tesseract 初始化成功")
            except Exception as e:
                print(f"    Tesseract 初始化失败: {e}")
                self.ocr_reader = None
    
    def extract_text_from_image(self, image, layer_name=""):
        """从图像中提取文字"""
        if not self.ocr_reader:
            return []
        
        try:
            # 图像预处理
            if isinstance(image, Image.Image):
                img_array = np.array(image)
            else:
                img_array = image
            
            # 转换RGBA到RGB
            if len(img_array.shape) == 3 and img_array.shape[2] == 4:
                rgb_array = np.ones((img_array.shape[0], img_array.shape[1], 3), dtype=np.uint8) * 255
                alpha = img_array[:, :, 3:4] / 255.0
                rgb_array = rgb_array * (1 - alpha) + img_array[:, :, :3] * alpha
                img_array = rgb_array.astype(np.uint8)
            
            text_results = []
            
            if OCR_ENGINE == "paddle":
                # 使用PaddleOCR
                result = self.ocr_reader.ocr(img_array, cls=True)
                
                if result and result[0]:
                    print(f"    -> PaddleOCR识别到 {len(result[0])} 个文字区域:")
                    
                    for i, line in enumerate(result[0]):
                        bbox_points = line[0]
                        text_info = line[1]
                        
                        text = text_info[0].strip()
                        confidence = text_info[1]
                        
                        # 计算边界框
                        x_coords = [point[0] for point in bbox_points]
                        y_coords = [point[1] for point in bbox_points]
                        x = int(min(x_coords))
                        y = int(min(y_coords))
                        w = int(max(x_coords) - min(x_coords))
                        h = int(max(y_coords) - min(y_coords))
                        
                        # 高质量过滤
                        if confidence > 0.7 and len(text) > 1 and self._is_valid_text(text):
                            print(f"      {i+1}. '{text}' (置信度: {confidence:.3f})")
                            text_results.append({
                                'text': text,
                                'confidence': confidence,
                                'bbox': (x, y, w, h),
                                'source': f'PaddleOCR识别-{layer_name}'
                            })
            
            elif OCR_ENGINE == "tesseract":
                # 使用Tesseract
                import pytesseract
                import cv2
                
                # 图像预处理
                if len(img_array.shape) == 3:
                    gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
                else:
                    gray = img_array
                
                # 自适应阈值
                binary = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
                
                # 多种配置
                configs = [
                    '--oem 3 --psm 6',
                    '--oem 3 --psm 8',
                    '--oem 3 --psm 7'
                ]
                
                all_results = []
                for config in configs:
                    try:
                        data = pytesseract.image_to_data(binary, config=config, 
                                                       output_type=pytesseract.Output.DICT, 
                                                       lang='chi_sim+eng')
                        
                        for i in range(len(data['text'])):
                            text = data['text'][i].strip()
                            conf = int(data['conf'][i]) if data['conf'][i] != -1 else 0
                            
                            if conf > 60 and len(text) > 1 and self._is_valid_text(text):
                                x, y, w, h = data['left'][i], data['top'][i], data['width'][i], data['height'][i]
                                all_results.append({
                                    'text': text,
                                    'confidence': conf / 100.0,
                                    'bbox': (x, y, w, h),
                                    'source': f'Tesseract识别-{layer_name}'
                                })
                    except:
                        continue
                
                # 去重
                seen_texts = set()
                for result in sorted(all_results, key=lambda x: x['confidence'], reverse=True):
                    if result['text'] not in seen_texts:
                        text_results.append(result)
                        seen_texts.add(result['text'])
                        print(f"      - '{result['text']}' (置信度: {result['confidence']:.3f})")
            
            return text_results
            
        except Exception as e:
            print(f"    OCR识别出错: {str(e)}")
            return []
    
    def _is_valid_text(self, text):
        """验证文本是否有效"""
        if not text or len(text.strip()) < 2:
            return False
        
        # 过滤纯符号
        meaningless_chars = set('.,;:!?@#$%^&*()[]{}|\\/<>~`')
        if all(c in meaningless_chars or c.isspace() for c in text):
            return False
        
        # 检查中文或有意义英文
        has_chinese = any('\u4e00' <= c <= '\u9fff' for c in text)
        has_meaningful_english = any(c.isalpha() for c in text) and len([c for c in text if c.isalpha()]) >= 2
        
        # 佛教关键词优先
        buddhist_keywords = ['普陀山', '观音', '菩萨', '佛', '早安', '善念', '佛佑', '众生']
        if any(keyword in text for keyword in buddhist_keywords):
            return True
        
        return has_chinese or has_meaningful_english

def process_psd(psd_path, base_output_folder):
    base_name = os.path.splitext(os.path.basename(psd_path))[0]
    psd_output_folder = os.path.join(base_output_folder, base_name)
    if not os.path.exists(psd_output_folder):
        os.makedirs(psd_output_folder)

    # 初始化OCR处理器
    ocr_processor = UltimateOCRProcessor()
    print(f"OCR功能状态: {OCR_TYPE}")

    psd = PSDImage.open(psd_path)
    all_layers = [layer for layer in psd.descendants() if layer.is_visible() and not layer.is_group()]

    # 找背景图层
    background_layer = None
    for layer in all_layers:
        if '背景' in layer.name or 'background' in layer.name.lower():
            background_layer = layer
            break
    
    if not background_layer:
        image_layers = [layer for layer in all_layers if layer.kind != 'type']
        if image_layers:
            background_layer = max(image_layers, key=lambda l: l.width * l.height)

    if background_layer:
        print(f"找到背景图层: '{background_layer.name}' ({background_layer.width}x{background_layer.height})")

    # 生成背景图和缩略图
    background_png = ''
    if background_layer:
        safe_layer_name = "".join([c if c.isalnum() or c in (' ', '_') else '_' for c in background_layer.name])
        background_png = f'{base_name}_background_{safe_layer_name}.png'
        background_path = os.path.join(psd_output_folder, background_png)
        image = background_layer.composite()
        if image:
            image.save(background_path)

    thumbnail_name = f'{base_name}_thumbnail.png'
    thumbnail_path = os.path.join(psd_output_folder, thumbnail_name)
    try:
        thumbnail = psd.composite()
        if thumbnail:
            thumbnail.thumbnail((300, 200))
            thumbnail.save(thumbnail_path)
            thumbnail_url = thumbnail_name
        else:
            thumbnail_url = ''
    except:
        thumbnail_url = ''

    # 生成INI内容
    ini_content = f"""[template:0]
name = {base_name}模板
thumbnail = {thumbnail_url}

[template:0:cards:0]
background = {background_png}

"""

    text_area_index = 0
    image_area_index = 0

    print(f"\n处理图层:")

    # 处理文本图层
    text_layers = [layer for layer in all_layers if layer.kind == 'type' and layer.is_visible()]
    for layer in text_layers:
        print(f"  处理文本图层: '{layer.name}'")
        text = layer.text
        left, top = layer.left, layer.top
        width, height = layer.width, layer.height

        ini_content += f"""[template:0:cards:0:textAreas:{text_area_index}]
content = {text}
color = #000000
maxLength = {max(50, len(text) + 20)}
position:x = {left}
position:y = {top}
size:width = {width}
size:height = {height}
font=楷体
fontSize=20
orientation=horizontal

"""
        text_area_index += 1
        print(f"    -> 已添加文字区域: '{text}'")

    # 处理图像图层
    image_layers = [layer for layer in all_layers if layer.kind != 'type' and not layer.is_group() and layer.is_visible()]
    for layer in image_layers:
        print(f"  处理图像图层: '{layer.name}' (尺寸: {layer.width}x{layer.height})")

        if layer.width <= 3 or layer.height <= 3:
            print(f"    -> 跳过: 图层尺寸太小")
            continue

        try:
            image = layer.composite()
            if image:
                safe_layer_name = "".join([c if c.isalnum() or c in (' ', '_') else '_' for c in layer.name])
                png_name = f'{base_name}_image_{image_area_index}_{safe_layer_name}.png'
                png_path = os.path.join(psd_output_folder, png_name)
                image.save(png_path)
                left, top = layer.left, layer.top
                width, height = layer.width, layer.height

                # OCR文字识别
                ocr_texts = []
                if ocr_processor.ocr_reader:
                    print(f"    -> 正在进行{OCR_TYPE}文字识别...")
                    print(f"    -> 图像信息: {image.size}, 模式: {image.mode}")

                    # 保存调试图像
                    debug_image_path = os.path.join(psd_output_folder, f"debug_{OCR_ENGINE}_{safe_layer_name}.png")
                    image.save(debug_image_path)
                    print(f"    -> 调试图像已保存: {debug_image_path}")

                    ocr_results = ocr_processor.extract_text_from_image(image, layer.name)

                    if ocr_results:
                        print(f"    -> ✅ 识别成功! 找到 {len(ocr_results)} 个文字:")
                        for result in ocr_results:
                            text = result['text']
                            confidence = result['confidence']
                            bbox = result['bbox']

                            ocr_x = left + bbox[0]
                            ocr_y = top + bbox[1]
                            ocr_w = bbox[2]
                            ocr_h = bbox[3]

                            estimated_font_size = max(12, min(48, int(ocr_h * 0.8)))

                            ini_content += f"""[template:0:cards:0:textAreas:{text_area_index}]
content = {text}
color = #000000
maxLength = {max(50, len(text) + 20)}
position:x = {ocr_x}
position:y = {ocr_y}
size:width = {ocr_w}
size:height = {ocr_h}
font=楷体
fontSize={estimated_font_size}
orientation=horizontal
source={result['source']}
confidence={confidence:.3f}

"""
                            text_area_index += 1
                            ocr_texts.append(text)
                    else:
                        print(f"    -> ❌ 未识别到有效文字内容")

                # 只有非背景图层才添加图像区域配置
                if layer != background_layer:
                    ini_content += f"""[template:0:cards:0:imageAreas:{image_area_index}]
url = {png_name}
position:x = {left}
position:y = {top}
size:width = {width}
size:height = {height}

"""
                    image_area_index += 1

                ocr_info = f" ({OCR_TYPE}识别: {len(ocr_texts)}个文字)" if ocr_texts else ""
                layer_type = "背景图层" if layer == background_layer else "图片图层"
                print(f"    -> 已处理{layer_type}: {layer.name}{ocr_info}")
            else:
                print(f"    -> 跳过: 无法生成图像")
        except Exception as e:
            print(f"    -> 跳过: 处理图像时出错 - {str(e)}")

    # 保存INI文件
    ini_path = os.path.join(psd_output_folder, f"{base_name}.ini")
    with open(ini_path, 'w', encoding='utf-8') as f:
        f.write(ini_content)

    # 统计
    ocr_count = ini_content.count('识别-')
    total_text_areas = ini_content.count(':textAreas:')

    print(f"\nINI配置文件已生成: {ini_path}")
    print(f"生成统计:")
    print(f"  - 图像区域: {image_area_index} 个")
    print(f"  - 文本区域: {total_text_areas} 个")
    print(f"    - PSD文本图层: {total_text_areas - ocr_count} 个")
    print(f"    - {OCR_TYPE}识别文字: {ocr_count} 个")
    print(f"  - 背景图片: {'1 个' if background_png else '0 个'}")
    print(f"  - 缩略图: {'1 个' if thumbnail_url else '0 个'}")

if __name__ == '__main__':
    if len(sys.argv) == 3:
        psd_path = os.path.abspath(sys.argv[1])
        output_folder = os.path.abspath(sys.argv[2])
        
        print(f"处理路径: {psd_path}")
        print(f"输出路径: {output_folder}")
        
        if os.path.isfile(psd_path) and psd_path.lower().endswith('.psd'):
            process_psd(psd_path, output_folder)
            print(f"处理完成: {psd_path}")
        else:
            print(f"错误: 无效的PSD文件路径: {psd_path}")
    else:
        print("用法: python ultimate_psd_processor.py <psd文件路径> <输出目录>")
