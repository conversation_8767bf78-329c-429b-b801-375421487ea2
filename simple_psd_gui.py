#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import subprocess
import sys

class SimplePSDGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("PSD处理工具")
        self.root.geometry("600x400")
        
        self.setup_ui()
        
    def setup_ui(self):
        # 主框架
        main_frame = tk.Frame(self.root, padx=20, pady=20)
        main_frame.pack(fill='both', expand=True)
        
        # 标题
        title_label = tk.Label(main_frame, text="PSD文件处理工具", 
                              font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # PSD文件选择
        file_frame = tk.Frame(main_frame)
        file_frame.pack(fill='x', pady=(0, 10))
        
        tk.Label(file_frame, text="PSD文件:").pack(side='left')
        
        self.file_var = tk.StringVar()
        file_entry = tk.Entry(file_frame, textvariable=self.file_var, width=40)
        file_entry.pack(side='left', padx=(10, 5), fill='x', expand=True)
        
        file_btn = tk.Button(file_frame, text="浏览", command=self.browse_file)
        file_btn.pack(side='right')
        
        # 输出目录选择
        output_frame = tk.Frame(main_frame)
        output_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(output_frame, text="输出目录:").pack(side='left')
        
        self.output_var = tk.StringVar(value="output")
        output_entry = tk.Entry(output_frame, textvariable=self.output_var, width=40)
        output_entry.pack(side='left', padx=(10, 5), fill='x', expand=True)
        
        output_btn = tk.Button(output_frame, text="浏览", command=self.browse_output)
        output_btn.pack(side='right')
        
        # 按钮区域
        button_frame = tk.Frame(main_frame)
        button_frame.pack(fill='x', pady=(0, 20))
        
        analyze_btn = tk.Button(button_frame, text="分析PSD", 
                               command=self.analyze_psd,
                               bg='#4CAF50', fg='white', 
                               font=('Arial', 10, 'bold'),
                               padx=20, pady=5)
        analyze_btn.pack(side='left', padx=(0, 10))
        
        process_btn = tk.Button(button_frame, text="生成配置", 
                               command=self.process_psd,
                               bg='#2196F3', fg='white', 
                               font=('Arial', 10, 'bold'),
                               padx=20, pady=5)
        process_btn.pack(side='left')
        
        # 结果显示区域
        result_frame = tk.LabelFrame(main_frame, text="处理结果")
        result_frame.pack(fill='both', expand=True)
        
        self.result_text = tk.Text(result_frame, height=15, width=70)
        scrollbar = tk.Scrollbar(result_frame, orient='horizontal', command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.pack(side='left', fill='both', expand=True, padx=(5, 0), pady=5)
        scrollbar.pack(side='right', fill='y', pady=5)
        
        # 初始化消息
        self.log("PSD处理工具已启动")
        self.log("请选择PSD文件开始处理...")
        
    def log(self, message):
        """添加日志消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.result_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.result_text.see(tk.END)
        self.root.update()
        
    def browse_file(self):
        """浏览PSD文件"""
        filename = filedialog.askopenfilename(
            title="选择PSD文件",
            filetypes=[("PSD文件", "*.psd"), ("所有文件", "*.*")]
        )
        if filename:
            self.file_var.set(filename)
            self.log(f"已选择文件: {os.path.basename(filename)}")
            
    def browse_output(self):
        """浏览输出目录"""
        dirname = filedialog.askdirectory(title="选择输出目录")
        if dirname:
            self.output_var.set(dirname)
            self.log(f"输出目录: {dirname}")
            
    def analyze_psd(self):
        """分析PSD文件"""
        psd_file = self.file_var.get()
        if not psd_file or not os.path.exists(psd_file):
            messagebox.showerror("错误", "请选择有效的PSD文件")
            return
            
        self.log("开始分析PSD文件...")
        
        try:
            # 调用分析脚本
            cmd = [sys.executable, "analyze_layers.py", psd_file]
            result = subprocess.run(cmd, capture_output=True, text=True, 
                                  cwd=os.path.dirname(os.path.abspath(__file__)))
            
            if result.returncode == 0:
                self.log("分析完成:")
                self.log(result.stdout)
            else:
                self.log(f"分析失败: {result.stderr}")
                
        except Exception as e:
            self.log(f"分析出错: {str(e)}")
            
    def process_psd(self):
        """处理PSD文件"""
        psd_file = self.file_var.get()
        output_dir = self.output_var.get()
        
        if not psd_file or not os.path.exists(psd_file):
            messagebox.showerror("错误", "请选择有效的PSD文件")
            return
            
        if not output_dir:
            messagebox.showerror("错误", "请设置输出目录")
            return
            
        self.log("开始处理PSD文件...")
        
        try:
            # 创建输出目录
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
                
            # 调用处理脚本
            script_content = f'''
import sys
import os
sys.path.append(r"{os.path.dirname(os.path.abspath(__file__))}")

# 设置文件路径
psd_file = r"{psd_file}"
output_folder = r"{output_dir}"

# 执行处理逻辑
exec(open("123.py").read())
'''
            
            result = subprocess.run([sys.executable, '-c', script_content], 
                                  capture_output=True, text=True,
                                  cwd=os.path.dirname(os.path.abspath(__file__)))
            
            if result.returncode == 0:
                self.log("处理完成!")
                if result.stdout:
                    self.log(result.stdout)
                    
                # 检查输出文件
                base_name = os.path.splitext(os.path.basename(psd_file))[0]
                output_path = os.path.join(output_dir, base_name)
                if os.path.exists(output_path):
                    self.log(f"输出文件夹: {output_path}")
                    
                    # 询问是否打开文件夹
                    if messagebox.askyesno("完成", "处理完成!\n是否打开输出文件夹?"):
                        try:
                            os.startfile(output_path)
                        except:
                            self.log("无法打开文件夹")
            else:
                self.log(f"处理失败: {result.stderr}")
                
        except Exception as e:
            self.log(f"处理出错: {str(e)}")

def main():
    root = tk.Tk()
    app = SimplePSDGUI(root)
    
    # 居中显示
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_reqwidth() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_reqheight() // 2)
    root.geometry(f"+{x}+{y}")
    
    root.mainloop()

if __name__ == "__main__":
    main()