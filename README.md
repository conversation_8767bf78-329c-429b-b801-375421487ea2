# PSD文件处理工具 - 正式版

一个专业的PSD文件处理工具，支持PSD文件解析、文本识别、图像提取和OCR文字识别功能。

## ✨ 主要功能

- **PSD文件解析** - 自动识别图层结构和文本内容
- **文本区域提取** - 精确提取PSD文本图层的位置和内容
- **图像区域提取** - 自动分离和保存图像图层
- **OCR文字识别** - 识别图片中的文字内容（支持中英文）
- **INI配置生成** - 生成标准格式的配置文件
- **缩略图生成** - 自动生成预览缩略图

## 🚀 快速开始

### 1. 安装依赖

```bash
python install_dependencies.py
```

### 2. 使用方法

#### GUI版本（推荐）
```bash
python psd_gui_tool.py
```

#### 命令行版本
```bash
python psd_processor.py <PSD文件路径> <输出目录>
```

### 3. 示例
```bash
# 处理单个PSD文件
python psd_processor.py example.psd output

# 启动GUI界面
python psd_gui_tool.py
```

## 📋 系统要求

- **Python**: 3.7 或更高版本
- **操作系统**: Windows / macOS / Linux
- **内存**: 建议 4GB 以上
- **Tesseract OCR**: 用于文字识别功能

## 📦 依赖包

- `psd-tools` - PSD文件解析
- `Pillow` - 图像处理
- `numpy` - 数值计算
- `opencv-python` - 计算机视觉
- `pytesseract` - OCR文字识别
- `tkinter` - GUI界面（Python内置）

## 🔧 安装Tesseract OCR

### Windows
1. 下载安装包：https://github.com/UB-Mannheim/tesseract/wiki
2. 安装到默认路径
3. 确保添加到系统PATH

### macOS
```bash
brew install tesseract
```

### Linux (Ubuntu/Debian)
```bash
sudo apt-get install tesseract-ocr
sudo apt-get install tesseract-ocr-chi-sim  # 中文支持
```

## 📁 输出文件结构

```
output/
└── 文件名/
    ├── 文件名.ini           # 主配置文件
    ├── 文件名_thumbnail.png # 缩略图
    ├── 文件名_background_背景.png # 背景图
    ├── 文件名_image_0_图层名.png  # 图像图层
    └── 文件名_image_1_图层名.png  # 图像图层
```

## 📄 INI配置文件格式

```ini
[template:0]
name = 模板名称
thumbnail = 缩略图文件名

[template:0:cards:0]
background = 背景图文件名

[template:0:cards:0:textAreas:0]
content = 文字内容
color = #000000
maxLength = 50
position:x = 100
position:y = 200
size:width = 300
size:height = 50
font = 楷体
fontSize = 20
orientation = horizontal

[template:0:cards:0:imageAreas:0]
url = 图像文件名
position:x = 100
position:y = 100
size:width = 200
size:height = 200
```

## 🎯 OCR功能说明

### 支持的文字类型
- **中文文字** - 简体中文识别
- **英文文字** - 英文字母和单词
- **数字** - 阿拉伯数字

### 识别质量优化
- **高置信度过滤** - 只保留置信度>70%的识别结果
- **智能文本验证** - 过滤无意义的字符组合
- **关键词优先** - 优先保留有意义的词汇
- **去重处理** - 自动去除重复识别结果

### 常见问题
1. **识别不到文字** - 检查图像中文字是否清晰
2. **识别结果不准确** - 确保文字与背景对比度足够
3. **中文识别效果差** - 确保安装了中文语言包

## 🛠️ 故障排除

### 1. 安装问题
```bash
# 升级pip
python -m pip install --upgrade pip

# 重新安装依赖
python install_dependencies.py
```

### 2. OCR问题
```bash
# 检查Tesseract安装
python -c "import pytesseract; print(pytesseract.get_tesseract_version())"

# 手动指定Tesseract路径（Windows）
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
```

### 3. PSD文件问题
- 确保PSD文件未损坏
- 检查文件路径中是否包含特殊字符
- 尝试用Photoshop重新保存PSD文件

## 📈 性能优化建议

1. **图像质量** - 使用高分辨率的PSD文件
2. **文字清晰度** - 确保文字图层清晰可读
3. **图层命名** - 使用有意义的图层名称
4. **文件大小** - 大文件处理时间较长，建议分批处理

## 🔄 更新日志

### v1.0.0 (正式版)
- ✅ 完整的PSD文件解析功能
- ✅ 高质量OCR文字识别
- ✅ 智能文本过滤和验证
- ✅ GUI和命令行双重支持
- ✅ 完善的错误处理和日志

## 📞 技术支持

如果遇到问题，请检查：
1. Python版本是否符合要求
2. 所有依赖是否正确安装
3. Tesseract OCR是否正常工作
4. PSD文件是否完整

## 📄 许可证

本项目仅供学习和研究使用。

---

**PSD文件处理工具** - 让PSD文件处理变得简单高效！
