#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
详细解释PSD图层识别和背景图层判断逻辑
"""

import os
import sys
from psd_tools import PSDImage

def analyze_psd_layers(psd_path):
    """详细分析PSD文件的图层结构"""
    print(f"🔍 详细分析PSD文件: {psd_path}")
    print("=" * 80)
    
    if not os.path.exists(psd_path):
        print(f"❌ 文件不存在: {psd_path}")
        return
    
    try:
        # 打开PSD文件
        psd = PSDImage.open(psd_path)
        print(f"📄 PSD文件信息:")
        print(f"   尺寸: {psd.width} x {psd.height} 像素")
        print(f"   颜色模式: {psd.color_mode}")
        print(f"   位深度: {psd.depth}")
        
        # 获取所有图层
        all_layers = [layer for layer in psd.descendants() if layer.is_visible() and not layer.is_group()]
        print(f"\n📊 图层统计:")
        print(f"   总可见图层数: {len(all_layers)}")
        
        # 分类图层
        text_layers = [layer for layer in all_layers if layer.kind == 'type']
        image_layers = [layer for layer in all_layers if layer.kind != 'type']
        
        print(f"   文本图层数: {len(text_layers)}")
        print(f"   图像图层数: {len(image_layers)}")
        
        print(f"\n📋 详细图层信息:")
        print("-" * 80)
        
        # 分析每个图层
        for i, layer in enumerate(all_layers):
            layer_type = "📝 文本图层" if layer.kind == 'type' else "🖼️ 图像图层"
            print(f"{i+1:2d}. {layer_type}")
            print(f"    名称: '{layer.name}'")
            print(f"    类型: {layer.kind}")
            print(f"    位置: ({layer.left}, {layer.top})")
            print(f"    尺寸: {layer.width} x {layer.height}")
            print(f"    可见: {layer.is_visible()}")
            print(f"    透明度: {layer.opacity}")
            
            if layer.kind == 'type':
                try:
                    text_content = layer.text
                    print(f"    文本内容: '{text_content}'")
                except:
                    print(f"    文本内容: 无法读取")
            
            print()
        
        print("=" * 80)
        print("🎯 背景图层判断逻辑:")
        print("-" * 80)
        
        # 模拟背景图层判断逻辑
        background_layer = None
        
        print("步骤1: 按名称查找背景图层")
        for layer in all_layers:
            print(f"   检查图层: '{layer.name}'")
            if '背景' in layer.name or 'background' in layer.name.lower():
                background_layer = layer
                print(f"   ✅ 找到背景图层: '{layer.name}' (按名称匹配)")
                break
            else:
                print(f"   ❌ 不是背景图层 (名称不匹配)")
        
        if not background_layer:
            print("\n步骤2: 按尺寸查找最大的图像图层")
            if image_layers:
                print("   图像图层尺寸排序:")
                sorted_layers = sorted(image_layers, key=lambda l: l.width * l.height, reverse=True)
                for i, layer in enumerate(sorted_layers):
                    area = layer.width * layer.height
                    marker = "👑 最大" if i == 0 else "  "
                    print(f"   {marker} '{layer.name}': {layer.width}x{layer.height} = {area:,} 像素")
                
                background_layer = sorted_layers[0]
                print(f"   ✅ 选择最大图层作为背景: '{background_layer.name}'")
            else:
                print("   ❌ 没有图像图层可作为背景")
        
        if background_layer:
            print(f"\n🎯 最终确定的背景图层:")
            print(f"   名称: '{background_layer.name}'")
            print(f"   尺寸: {background_layer.width} x {background_layer.height}")
            print(f"   面积: {background_layer.width * background_layer.height:,} 像素")
            
            print(f"\n📝 处理逻辑说明:")
            safe_bg_name = "".join([c if c.isalnum() or c in (' ', '_') else '_' for c in background_layer.name])
            print(f"   1. 背景图层将单独保存为: 文件名_background_{safe_bg_name}.png")
            print(f"   2. 背景图层不会重复保存为图像区域")
            print(f"   3. 其他图像图层将保存为: 文件名_image_索引_图层名.png")
            print(f"   4. INI配置中background字段指向背景图层文件")
            print(f"   5. INI配置中imageAreas只包含非背景图层")
        else:
            print(f"\n⚠️ 未找到合适的背景图层")
        
        print(f"\n🔄 图层处理顺序:")
        print("-" * 80)
        
        # 模拟处理顺序
        print("1. 处理文本图层:")
        for i, layer in enumerate(text_layers):
            print(f"   {i+1}. '{layer.name}' -> INI textAreas:{i}")
        
        print("\n2. 处理图像图层:")
        image_area_index = 0
        for i, layer in enumerate(image_layers):
            if layer == background_layer:
                print(f"   {i+1}. '{layer.name}' -> 跳过 (已作为背景处理)")
            else:
                print(f"   {i+1}. '{layer.name}' -> INI imageAreas:{image_area_index}")
                image_area_index += 1
        
        print(f"\n📊 最终输出文件预览:")
        print("-" * 80)
        base_name = os.path.splitext(os.path.basename(psd_path))[0]
        
        print(f"配置文件:")
        print(f"   {base_name}.ini")
        
        print(f"\n图片文件:")
        print(f"   {base_name}_thumbnail.png  (缩略图)")
        
        if background_layer:
            safe_bg_name = "".join([c if c.isalnum() or c in (' ', '_') else '_' for c in background_layer.name])
            print(f"   {base_name}_background_{safe_bg_name}.png  (背景图)")
        
        image_area_index = 0
        for layer in image_layers:
            if layer != background_layer:
                safe_layer_name = "".join([c if c.isalnum() or c in (' ', '_') else '_' for c in layer.name])
                if len(safe_layer_name) > 50:
                    safe_layer_name = safe_layer_name[:50]
                print(f"   {base_name}_image_{image_area_index}_{safe_layer_name}.png  (图像区域)")
                image_area_index += 1
        
    except Exception as e:
        print(f"❌ 分析PSD文件时出错: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🔍 PSD图层识别和背景判断逻辑详解")
    print("=" * 80)
    
    # 检查命令行参数
    if len(sys.argv) != 2:
        print("用法: python explain_layer_detection.py <PSD文件路径>")
        print("\n示例:")
        print("   python explain_layer_detection.py 11.psd")
        print("   python explain_layer_detection.py example.psd")
        return
    
    psd_path = sys.argv[1]
    
    # 如果路径不存在，尝试在当前目录查找
    if not os.path.exists(psd_path):
        current_dir_path = os.path.join(".", psd_path)
        if os.path.exists(current_dir_path):
            psd_path = current_dir_path
        else:
            print(f"❌ 找不到PSD文件: {psd_path}")
            print(f"请确保文件路径正确")
            return
    
    # 分析PSD文件
    analyze_psd_layers(psd_path)
    
    print(f"\n💡 背景图层判断规则总结:")
    print("=" * 80)
    print("1. 优先级1: 按名称匹配")
    print("   - 图层名包含 '背景' 的图层")
    print("   - 图层名包含 'background' 的图层 (不区分大小写)")
    print()
    print("2. 优先级2: 按尺寸匹配")
    print("   - 如果没有按名称找到背景图层")
    print("   - 选择面积最大的图像图层作为背景")
    print("   - 面积 = 宽度 × 高度")
    print()
    print("3. 处理原则:")
    print("   - 背景图层只保存一次，作为background使用")
    print("   - 背景图层不会重复保存为imageAreas")
    print("   - 其他图像图层按顺序保存为imageAreas")
    print("   - 文本图层单独处理为textAreas")
    print()
    print("4. 文件命名规则:")
    print("   - 背景图: 文件名_background_图层名.png")
    print("   - 图像区域: 文件名_image_索引_图层名.png")
    print("   - 缩略图: 文件名_thumbnail.png")
    print("   - 配置文件: 文件名.ini")

if __name__ == "__main__":
    try:
        main()
        input(f"\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n⏹️ 分析被用户中断")
    except Exception as e:
        print(f"\n❌ 分析过程中出现错误: {e}")
        input("\n按回车键退出...")
