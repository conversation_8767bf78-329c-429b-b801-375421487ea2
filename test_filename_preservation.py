#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试文件名保留功能
验证Web版本是否正确保留原始文件名
"""

import os
import sys
import time
import requests
import json

def test_filename_preservation():
    """测试文件名保留功能"""
    print("🧪 测试文件名保留功能")
    print("=" * 40)
    
    # 检查Web服务器是否运行
    try:
        response = requests.get('http://localhost:5000', timeout=5)
        print("✅ Web服务器正在运行")
    except requests.exceptions.RequestException:
        print("❌ Web服务器未运行，请先启动: python web_app.py")
        return False
    
    # 测试文件列表
    test_files = [
        "example.psd",
        "测试文件.psd", 
        "My Project File.psd",
        "项目-设计稿-v1.0.psd"
    ]
    
    print(f"\n📋 将测试以下文件名:")
    for filename in test_files:
        print(f"  - {filename}")
    
    print(f"\n🔍 测试流程:")
    print("1. 模拟文件上传")
    print("2. 检查服务器响应中的文件名")
    print("3. 验证是否保留原始文件名")
    
    # 创建测试用的PSD文件内容（简单的二进制数据）
    test_psd_content = b"8BPS\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00"  # PSD文件头
    
    success_count = 0
    
    for filename in test_files:
        print(f"\n📤 测试文件: {filename}")
        
        try:
            # 模拟文件上传
            files = {
                'file': (filename, test_psd_content, 'application/octet-stream')
            }
            
            response = requests.post('http://localhost:5000/upload', files=files, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                # 检查响应中的文件名
                if 'filename' in data and data['filename'] == filename:
                    print(f"  ✅ 文件名保留正确: {data['filename']}")
                    print(f"  📝 服务器消息: {data['message']}")
                    success_count += 1
                    
                    # 获取任务ID并检查状态
                    task_id = data['task_id']
                    print(f"  🆔 任务ID: {task_id}")
                    
                    # 等待一下再检查状态
                    time.sleep(1)
                    
                    status_response = requests.get(f'http://localhost:5000/status/{task_id}', timeout=5)
                    if status_response.status_code == 200:
                        status_data = status_response.json()
                        original_filename = status_data.get('original_filename', '未找到')
                        print(f"  📄 任务中保存的原始文件名: {original_filename}")
                        
                        if original_filename == filename:
                            print(f"  ✅ 任务状态中文件名保留正确")
                        else:
                            print(f"  ❌ 任务状态中文件名不匹配")
                    
                else:
                    print(f"  ❌ 文件名保留失败")
                    print(f"     期望: {filename}")
                    print(f"     实际: {data.get('filename', '未找到')}")
                    
            else:
                print(f"  ❌ 上传失败: HTTP {response.status_code}")
                print(f"     响应: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"  ❌ 请求失败: {e}")
        except Exception as e:
            print(f"  ❌ 测试出错: {e}")
    
    print(f"\n📊 测试结果:")
    print(f"  总测试数: {len(test_files)}")
    print(f"  成功数: {success_count}")
    print(f"  成功率: {success_count/len(test_files)*100:.1f}%")
    
    if success_count == len(test_files):
        print("🎉 所有测试通过！文件名保留功能正常工作")
        return True
    else:
        print("⚠️ 部分测试失败，请检查Web应用代码")
        return False

def test_directory_structure():
    """测试目录结构"""
    print("\n📁 检查目录结构...")
    
    directories = ['uploads', 'outputs', 'templates']
    
    for directory in directories:
        if os.path.exists(directory):
            print(f"  ✅ {directory}/ - 存在")
            
            # 检查uploads目录中的任务子目录
            if directory == 'uploads':
                subdirs = [d for d in os.listdir(directory) if os.path.isdir(os.path.join(directory, d))]
                if subdirs:
                    print(f"     📂 发现 {len(subdirs)} 个任务目录:")
                    for subdir in subdirs[:3]:  # 只显示前3个
                        task_dir = os.path.join(directory, subdir)
                        files = os.listdir(task_dir)
                        print(f"       - {subdir}/ ({len(files)} 个文件)")
                        for file in files:
                            print(f"         📄 {file}")
                    if len(subdirs) > 3:
                        print(f"       ... 还有 {len(subdirs) - 3} 个目录")
        else:
            print(f"  ❌ {directory}/ - 不存在")

def cleanup_test_files():
    """清理测试文件"""
    print("\n🧹 清理测试文件...")
    
    try:
        response = requests.get('http://localhost:5000/cleanup', timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"  ✅ {data['message']}")
        else:
            print(f"  ⚠️ 清理请求失败: HTTP {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"  ⚠️ 清理请求失败: {e}")

def main():
    """主函数"""
    print("🧪 PSD处理工具Web版 - 文件名保留功能测试")
    print("=" * 60)
    
    # 1. 测试文件名保留
    success = test_filename_preservation()
    
    # 2. 检查目录结构
    test_directory_structure()
    
    # 3. 清理测试文件
    cleanup_test_files()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试完成！文件名保留功能正常工作")
        print("\n✨ 改进效果:")
        print("  - ✅ 保留原始文件名，不添加GUID前缀")
        print("  - ✅ 使用任务目录避免文件名冲突")
        print("  - ✅ 下载文件名基于原始文件名")
        print("  - ✅ 界面显示原始文件名信息")
    else:
        print("⚠️ 测试发现问题，请检查代码")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        input(f"\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        input("\n按回车键退出...")
