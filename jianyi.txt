以下是为你现有 extract_text_font_info() 做的精准增强方案，解决你遇到的字体名和字号提取不准确的问题。该方案：

✅ 优先使用 Adobe 官方推荐的 engine_dict.ResourceDict.FontSet 结构获取字体名；

✅ 避免样式字段混乱导致字体名错误；

✅ 兼容 float 类型字号并四舍五入；

✅ 与你现有逻辑兼容，优先命中快速准确路径，未命中再走现有逻辑；

✅ 【增强后的字体提取函数】替换你的 extract_text_font_info 函数
python
复制
编辑
def extract_text_font_info(layer):
    """
    从PSD文本图层中提取字体信息 - 增强推荐版
    优先从 engine_dict.ResourceDict.FontSet 中提取字体名与字号
    """
    print(f"        提取字体信息开始: '{layer.name}'")

    default_font = '宋体'
    default_size = 20
    font_name = default_font
    font_size = default_size

    try:
        if hasattr(layer, 'engine_dict') and layer.engine_dict:
            engine_dict = layer.engine_dict

            # 路径：engine_dict → ResourceDict → FontSet
            font_set = engine_dict.get('ResourceDict', {}).get('FontSet', [])
            run_array = engine_dict.get('StyleRun', {}).get('RunArray', [])

            if font_set and run_array:
                first_run = run_array[0].get('StyleSheet', {})
                font_index = first_run.get('Font')
                font_size_val = first_run.get('FontSize')

                if isinstance(font_index, int) and font_index < len(font_set):
                    raw_font_name = font_set[font_index].get('Name')
                    if raw_font_name:
                        font_name = map_font_name(raw_font_name)
                        print(f"          ✓ 从 FontSet 提取字体: {raw_font_name} → {font_name}")

                if isinstance(font_size_val, (int, float)) and font_size_val > 0:
                    font_size = int(round(font_size_val))
                    print(f"          ✓ 从 StyleSheet 提取字号: {font_size_val} → {font_size}pt")

                return font_name, font_size

    except Exception as e:
        print(f"        ✗ 引擎提取失败，进入备用提取路径: {e}")

    # 如果 engine_dict 方式失败，回退到你的原始深度逻辑（不改动你原来的方法结构）
    try:
        return fallback_extract_font_info(layer)
    except Exception as e:
        print(f"        ✗ 后备提取也失败: {e}")
        return font_name, font_size
✅ 【新增】原始逻辑包为 fallback 函数
把你原来 extract_text_font_info() 中 95% 的内容（从 text_data 结构中查找等）复制粘贴为一个新函数 fallback_extract_font_info()，在增强版中作为备用方案调用。

python
复制
编辑
def fallback_extract_font_info(layer):
    """
    原始字体提取逻辑（你现有的完整深度逻辑，作为 fallback）
    """
    # ← 把你原来的 extract_text_font_info() 主体复制进来，函数签名保留，注释改掉即可
    ...
✅ 总结优势
优化点	效果
✅ 优先提取官方路径	更加稳定、准确，尤其是 Adobe 系工具保存的 PSD
✅ 减少字段名猜测	避免不必要的 FontName, PostScriptName 猜测
✅ 字号支持 float 精度	准确还原设计稿字号
✅ 兼容你原有逻辑	不会影响已有自定义 fallback（图层名/内容/尺寸推测等）