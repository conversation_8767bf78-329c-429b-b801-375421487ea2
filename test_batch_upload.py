#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试多文件上传和时间戳功能
"""

import os
import sys
import time
import requests
import json
from datetime import datetime

def create_test_psd_files():
    """创建测试用的PSD文件"""
    test_files = []
    test_psd_content = b"8BPS\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00"  # PSD文件头
    
    filenames = [
        "项目A-设计稿.psd",
        "项目B-界面设计.psd", 
        "测试文件-多卡片.psd"
    ]
    
    for filename in filenames:
        test_files.append((filename, test_psd_content))
    
    return test_files

def test_single_file_upload():
    """测试单文件上传"""
    print("📄 测试单文件上传...")
    
    test_files = create_test_psd_files()
    filename, content = test_files[0]
    
    try:
        files = {'file': (filename, content, 'application/octet-stream')}
        response = requests.post('http://localhost:5000/upload', files=files, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"  ✅ 单文件上传成功")
            print(f"     文件名: {data.get('filename', '未知')}")
            print(f"     任务ID: {data.get('task_id', '未知')}")
            print(f"     是否批量: {data.get('is_batch', False)}")
            
            # 检查时间戳
            task_id = data['task_id']
            time.sleep(2)  # 等待处理
            
            status_response = requests.get(f'http://localhost:5000/status/{task_id}', timeout=5)
            if status_response.status_code == 200:
                status_data = status_response.json()
                if 'result' in status_data and 'processed_at' in status_data['result']:
                    timestamp = status_data['result']['processed_at']
                    print(f"     时间戳: {timestamp}")
                    
                    # 验证时间戳格式
                    try:
                        datetime.strptime(timestamp, "%Y%m%d_%H%M%S")
                        print(f"     ✅ 时间戳格式正确")
                    except ValueError:
                        print(f"     ❌ 时间戳格式错误")
            
            return True
        else:
            print(f"  ❌ 单文件上传失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"  ❌ 单文件上传出错: {e}")
        return False

def test_multiple_file_upload():
    """测试多文件上传"""
    print("\n📚 测试多文件上传...")
    
    test_files = create_test_psd_files()
    
    try:
        # 准备多文件上传
        files = []
        for filename, content in test_files:
            files.append(('files', (filename, content, 'application/octet-stream')))
        
        response = requests.post('http://localhost:5000/upload', files=files, timeout=15)
        
        if response.status_code == 200:
            data = response.json()
            print(f"  ✅ 多文件上传成功")
            print(f"     文件数量: {data.get('total_files', '未知')}")
            print(f"     任务ID: {data.get('task_id', '未知')}")
            print(f"     是否批量: {data.get('is_batch', False)}")
            
            # 监控批量处理进度
            task_id = data['task_id']
            print(f"  🔄 监控批量处理进度...")
            
            max_wait = 30  # 最多等待30秒
            start_time = time.time()
            
            while time.time() - start_time < max_wait:
                status_response = requests.get(f'http://localhost:5000/status/{task_id}', timeout=5)
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    progress = status_data.get('progress', 0)
                    message = status_data.get('message', '处理中...')
                    status = status_data.get('status', 'unknown')
                    
                    print(f"     进度: {progress}% - {message}")
                    
                    if status == 'completed':
                        print(f"  ✅ 批量处理完成!")
                        
                        # 检查批量结果
                        if 'result' in status_data:
                            result = status_data['result']
                            print(f"     处理文件数: {result.get('total_files', 0)}")
                            print(f"     成功文件数: {result.get('completed_files', 0)}")
                            print(f"     时间戳: {result.get('processed_at', '未知')}")
                            
                            # 检查文件详情
                            if 'file_results' in result:
                                print(f"     📋 文件处理详情:")
                                for file_result in result['file_results']:
                                    filename = file_result.get('filename', '未知')
                                    image_areas = file_result.get('image_areas', 0)
                                    text_areas = file_result.get('text_areas', 0)
                                    ocr_count = file_result.get('ocr_count', 0)
                                    print(f"       - {filename}: 图像{image_areas} | 文本{text_areas} | OCR{ocr_count}")
                        
                        return True
                    elif status == 'error':
                        print(f"  ❌ 批量处理失败: {message}")
                        return False
                
                time.sleep(2)
            
            print(f"  ⏰ 等待超时，但任务可能仍在处理中")
            return False
            
    except Exception as e:
        print(f"  ❌ 多文件上传出错: {e}")
        return False

def test_filename_with_timestamp():
    """测试文件名时间戳功能"""
    print("\n🕐 测试文件名时间戳功能...")
    
    test_files = create_test_psd_files()
    filename, content = test_files[0]
    
    try:
        files = {'file': (filename, content, 'application/octet-stream')}
        response = requests.post('http://localhost:5000/upload', files=files, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            task_id = data['task_id']
            
            # 等待处理完成
            max_wait = 15
            start_time = time.time()
            
            while time.time() - start_time < max_wait:
                status_response = requests.get(f'http://localhost:5000/status/{task_id}', timeout=5)
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    
                    if status_data.get('status') == 'completed' and 'result' in status_data:
                        result = status_data['result']
                        zip_filename = result.get('zip_display_name', '')
                        
                        print(f"  📦 生成的ZIP文件名: {zip_filename}")
                        
                        # 检查文件名格式
                        expected_pattern = f"{os.path.splitext(filename)[0]}_处理结果_"
                        if expected_pattern in zip_filename:
                            print(f"  ✅ 文件名包含原始名称和时间戳")
                            
                            # 提取时间戳部分
                            timestamp_part = zip_filename.replace(expected_pattern, '').replace('.zip', '')
                            try:
                                datetime.strptime(timestamp_part, "%Y%m%d_%H%M%S")
                                print(f"  ✅ 时间戳格式正确: {timestamp_part}")
                                return True
                            except ValueError:
                                print(f"  ❌ 时间戳格式错误: {timestamp_part}")
                                return False
                        else:
                            print(f"  ❌ 文件名格式不符合预期")
                            return False
                
                time.sleep(1)
            
            print(f"  ⏰ 等待处理超时")
            return False
            
    except Exception as e:
        print(f"  ❌ 测试时间戳功能出错: {e}")
        return False

def test_web_server_status():
    """测试Web服务器状态"""
    print("🌐 检查Web服务器状态...")
    
    try:
        response = requests.get('http://localhost:5000', timeout=5)
        if response.status_code == 200:
            print("  ✅ Web服务器正在运行")
            return True
        else:
            print(f"  ❌ Web服务器响应异常: HTTP {response.status_code}")
            return False
    except requests.exceptions.RequestException:
        print("  ❌ Web服务器未运行，请先启动: python web_app.py")
        return False

def main():
    """主函数"""
    print("🧪 PSD处理工具Web版 - 多文件上传和时间戳功能测试")
    print("=" * 70)
    
    # 1. 检查Web服务器
    if not test_web_server_status():
        return False
    
    # 2. 测试单文件上传
    single_success = test_single_file_upload()
    
    # 3. 测试多文件上传
    batch_success = test_multiple_file_upload()
    
    # 4. 测试时间戳功能
    timestamp_success = test_filename_with_timestamp()
    
    # 5. 清理测试文件
    print(f"\n🧹 清理测试文件...")
    try:
        cleanup_response = requests.get('http://localhost:5000/cleanup', timeout=10)
        if cleanup_response.status_code == 200:
            cleanup_data = cleanup_response.json()
            print(f"  ✅ {cleanup_data.get('message', '清理完成')}")
        else:
            print(f"  ⚠️ 清理请求失败")
    except:
        print(f"  ⚠️ 清理请求出错")
    
    # 6. 总结
    print("\n" + "=" * 70)
    print("📊 测试结果总结:")
    print(f"  单文件上传: {'✅ 通过' if single_success else '❌ 失败'}")
    print(f"  多文件上传: {'✅ 通过' if batch_success else '❌ 失败'}")
    print(f"  时间戳功能: {'✅ 通过' if timestamp_success else '❌ 失败'}")
    
    all_success = single_success and batch_success and timestamp_success
    
    if all_success:
        print("\n🎉 所有测试通过！新功能正常工作")
        print("\n✨ 新增功能:")
        print("  - ✅ 支持多文件批量上传")
        print("  - ✅ 输出文件名包含时间戳")
        print("  - ✅ 批量处理进度显示")
        print("  - ✅ 批量结果统计和详情")
    else:
        print("\n⚠️ 部分测试失败，请检查代码")
    
    return all_success

if __name__ == "__main__":
    try:
        success = main()
        input(f"\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        input("\n按回车键退出...")
