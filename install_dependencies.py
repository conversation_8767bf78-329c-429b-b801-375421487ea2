#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
PSD处理工具依赖安装脚本
"""

import subprocess
import sys
import os

def run_command(command):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, encoding='utf-8')
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def install_package(package_name, description=""):
    """安装Python包"""
    print(f"\n📦 正在安装 {package_name}...")
    if description:
        print(f"   {description}")
    
    success, stdout, stderr = run_command(f"{sys.executable} -m pip install {package_name}")
    
    if success:
        print(f"✅ {package_name} 安装成功!")
        return True
    else:
        print(f"❌ {package_name} 安装失败:")
        print(f"   错误信息: {stderr}")
        return False

def check_package(package_name):
    """检查包是否已安装"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False

def main():
    print("🚀 PSD处理工具依赖安装")
    print("=" * 40)
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"🐍 Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 7):
        print("❌ 错误: 需要Python 3.7或更高版本")
        return False
    
    # 升级pip
    print("\n🔧 升级pip...")
    run_command(f"{sys.executable} -m pip install --upgrade pip")
    
    # 必需的依赖包
    required_packages = [
        ("psd-tools", "PSD文件解析库"),
        ("Pillow", "Python图像处理库"),
        ("numpy", "数值计算库"),
        ("opencv-python", "计算机视觉库"),
        ("pytesseract", "Tesseract OCR Python接口")
    ]
    
    print("\n📚 安装必需依赖...")
    
    failed_packages = []
    for package, desc in required_packages:
        package_name = package.replace('-', '_')
        if not check_package(package_name):
            if not install_package(package, desc):
                failed_packages.append(package)
        else:
            print(f"✅ {package} 已安装")
    
    # 检查Tesseract可执行文件
    print("\n🔍 检查Tesseract OCR...")
    try:
        import pytesseract
        # 尝试获取版本信息
        version = pytesseract.get_tesseract_version()
        print(f"✅ Tesseract OCR 版本: {version}")
    except Exception as e:
        print(f"⚠️ Tesseract OCR 检查失败: {e}")
        print("   请确保已安装Tesseract OCR:")
        print("   Windows: https://github.com/UB-Mannheim/tesseract/wiki")
        print("   或下载: https://digi.bib.uni-mannheim.de/tesseract/")
    
    # 测试核心功能
    print("\n🧪 测试核心功能...")
    
    try:
        from psd_tools import PSDImage
        print("✅ PSD文件解析功能正常")
    except Exception as e:
        print(f"❌ PSD文件解析功能异常: {e}")
        failed_packages.append("psd-tools")
    
    try:
        from PIL import Image
        import numpy as np
        import cv2
        print("✅ 图像处理功能正常")
    except Exception as e:
        print(f"❌ 图像处理功能异常: {e}")
        failed_packages.append("图像处理库")
    
    # 总结
    print("\n" + "=" * 40)
    print("📋 安装总结:")
    
    if not failed_packages:
        print("✅ 所有依赖安装成功!")
        print("🎉 PSD处理工具已就绪!")
        print("\n🚀 使用方法:")
        print("   python psd_gui_tool.py          # GUI版本")
        print("   python psd_processor.py <psd文件> <输出目录>  # 命令行版本")
    else:
        print(f"❌ 以下包安装失败: {', '.join(failed_packages)}")
        print("   请检查网络连接或手动安装")
    
    return len(failed_packages) == 0

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎉 安装完成! 工具已就绪")
        else:
            print("\n⚠️ 安装完成，但部分功能可能不可用")
        
        input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n❌ 安装被用户中断")
    except Exception as e:
        print(f"\n❌ 安装过程中出现错误: {e}")
        input("\n按回车键退出...")
