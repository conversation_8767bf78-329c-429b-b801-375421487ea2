#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速PaddleOCR测试
"""

def test_paddleocr():
    """快速测试PaddleOCR"""
    print("🔤 快速PaddleOCR测试")
    print("=" * 30)
    
    try:
        print("1. 导入PaddleOCR...")
        from paddleocr import PaddleOCR
        print("   ✅ 导入成功")
        
        print("2. 初始化OCR引擎...")
        # 使用最简单的配置
        ocr = PaddleOCR(lang='ch')
        print("   ✅ 初始化成功")
        
        print("3. 创建测试图像...")
        from PIL import Image, ImageDraw, ImageFont
        import numpy as np
        
        # 创建简单的测试图像
        img = Image.new('RGB', (200, 100), 'white')
        draw = ImageDraw.Draw(img)
        
        # 使用默认字体绘制文字
        try:
            font = ImageFont.truetype("C:/Windows/Fonts/simhei.ttf", 24)
        except:
            font = ImageFont.load_default()
        
        draw.text((20, 30), "普陀山", fill='black', font=font)
        draw.text((20, 60), "观音", fill='black', font=font)
        
        # 保存测试图像
        img.save("paddle_test.png")
        print("   ✅ 测试图像已创建: paddle_test.png")
        
        print("4. 进行OCR识别...")
        img_array = np.array(img)
        result = ocr.ocr(img_array, cls=True)
        
        if result and result[0]:
            print(f"   ✅ 识别成功! 找到 {len(result[0])} 个文字区域:")
            for i, line in enumerate(result[0], 1):
                text = line[1][0]
                confidence = line[1][1]
                print(f"      {i}. '{text}' (置信度: {confidence:.3f})")
            return True
        else:
            print("   ⚠️ 未识别到文字")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def test_psd_processing():
    """测试PSD文件处理"""
    print("\n📄 测试PSD处理...")
    
    import os
    psd_file = "未标题-1.psd"
    
    if not os.path.exists(psd_file):
        print(f"   ⚠️ 未找到PSD文件: {psd_file}")
        return False
    
    try:
        print(f"   🔧 处理PSD文件: {psd_file}")
        
        import subprocess
        import sys
        
        result = subprocess.run([
            sys.executable, "123_paddle.py", psd_file, "output_paddle_test"
        ], capture_output=True, text=True, encoding='utf-8', errors='ignore')
        
        if result.returncode == 0:
            print("   ✅ PSD处理成功!")
            
            # 检查结果
            ini_file = "output_paddle_test/未标题-1/未标题-1.ini"
            if os.path.exists(ini_file):
                with open(ini_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                paddle_count = content.count('source=PaddleOCR识别')
                print(f"   📊 PaddleOCR识别到: {paddle_count} 个文字区域")
                
                if paddle_count > 0:
                    print("   🎉 成功识别到文字!")
                    
                    # 显示识别的文字
                    lines = content.split('\n')
                    for i, line in enumerate(lines):
                        if 'source=PaddleOCR识别' in line:
                            for j in range(max(0, i-10), min(len(lines), i+1)):
                                if lines[j].startswith('content = '):
                                    text = lines[j].replace('content = ', '')
                                    print(f"      - '{text}'")
                                    break
                else:
                    print("   ℹ️ 未识别到文字（可能图像中没有清晰文字）")
                
                return True
            else:
                print("   ❌ 未找到生成的INI文件")
                return False
        else:
            print("   ❌ PSD处理失败")
            print(f"   错误: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"   ❌ 处理出错: {e}")
        return False

def main():
    """主函数"""
    print("🚀 PaddleOCR快速验证")
    print("=" * 40)
    
    # 测试基本功能
    basic_test = test_paddleocr()
    
    # 测试PSD处理
    psd_test = test_psd_processing()
    
    # 总结
    print(f"\n{'='*40}")
    print("📋 测试结果:")
    print(f"  - 基本OCR功能: {'✅ 正常' if basic_test else '❌ 异常'}")
    print(f"  - PSD文件处理: {'✅ 正常' if psd_test else '❌ 异常'}")
    
    if basic_test and psd_test:
        print("\n🎉 PaddleOCR功能完全正常!")
        print("现在可以使用GUI工具或命令行工具处理PSD文件了")
    elif basic_test:
        print("\n⚠️ 基本功能正常，但PSD处理有问题")
        print("请检查PSD文件是否存在")
    else:
        print("\n❌ PaddleOCR基本功能异常")
        print("请检查安装是否正确")
    
    # 清理测试文件
    try:
        import os
        if os.path.exists("paddle_test.png"):
            os.remove("paddle_test.png")
            print("\n🗑️ 已清理测试文件")
    except:
        pass
    
    return basic_test and psd_test

if __name__ == "__main__":
    try:
        success = main()
        
        if success:
            print(f"\n🚀 推荐使用方式:")
            print("   python psd_gui_tool.py  # GUI版本")
            print("   python 123_paddle.py 未标题-1.psd output  # 命令行版本")
        
        input("\n按回车键退出...")
        
    except KeyboardInterrupt:
        print("\n\n❌ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        input("\n按回车键退出...")
